package viclass.editor.geo.dbentity.elementdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "EllipseGeometry", key = "geoDataType")
data class EllipseGeometry @BsonCreator constructor(
    @BsonProperty("version")
    override var version: Int,

    @BsonProperty("f1PointIdx")
    var f1PointIdx: Int? = null,

    @BsonProperty("f2PointIdx")
    var f2PointIdx: Int? = null,

    @BsonProperty("a")
    var a: Double,

    @BsonProperty("b")
    var b: Double,

    // New fields for center-vector mode support
    @BsonProperty("centerPointIdx")
    var centerPointIdx: Int? = null,

    @BsonProperty("vectorAPointIdx")
    var vectorAPointIdx: Int? = null,

    @BsonProperty("vectorBPointIdx")
    var vectorBPointIdx: Int? = null

) : GeometryData