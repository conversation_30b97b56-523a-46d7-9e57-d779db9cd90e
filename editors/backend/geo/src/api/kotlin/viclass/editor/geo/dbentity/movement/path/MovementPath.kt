package viclass.editor.geo.dbentity.movement.path

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import org.bson.codecs.pojo.annotations.BsonDiscriminator

/**
 * This interface describes the way of storing transform movement path of moving an element
 */
@BsonDiscriminator(key = "type")
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = MovementFreePath::class),
    JsonSubTypes.Type(value = MovementLinePath::class),
    JsonSubTypes.Type(value = MovementLineSegmentPath::class),
    JsonSubTypes.Type(value = MovementSectorPath::class),
    JsonSubTypes.Type(value = MovementCirclePath::class),
    JsonSubTypes.Type(value = MovementEllipsePath::class),
)
interface MovementPath {
    fun clone(): MovementPath
}