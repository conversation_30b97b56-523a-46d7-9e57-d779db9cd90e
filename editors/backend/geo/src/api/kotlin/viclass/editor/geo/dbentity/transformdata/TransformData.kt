package viclass.editor.geo.dbentity.transformdata

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import org.bson.codecs.pojo.annotations.BsonDiscriminator

/**
 * This interface describes the way of storing transform data of moving an element
 */
@BsonDiscriminator(key = "type")
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = FreePointTransformData::class),
    JsonSubTypes.Type(value = DistanceTransformData::class),
    JsonSubTypes.Type(value = PointOnLineWithCoefficientTransformData::class),
    JsonSubTypes.Type(value = PointOnLineWithLengthTransformData::class),
    JsonSubTypes.Type(value = PointOnArcWithAngleTransformData::class),
    JsonSubTypes.Type(value = PointOnEllipseTransformData::class),
    JsonSubTypes.Type(value = MoveOrderTransformData::class),
    JsonSubTypes.Type(value = MoveAngleTransformData::class),
    JsonSubTypes.Type(value = RhombusVertexTransformData::class),
)
interface TransformData {
    fun clone(): TransformData
}