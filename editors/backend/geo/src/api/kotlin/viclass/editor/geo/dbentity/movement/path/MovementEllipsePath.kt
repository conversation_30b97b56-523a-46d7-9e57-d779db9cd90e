package viclass.editor.geo.dbentity.movement.path

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class MovementEllipsePath @BsonCreator constructor(
    @BsonProperty("center") val center: DoubleArray,
    @BsonProperty("f1") val f1: DoubleArray,
    @BsonProperty("f2") val f2: DoubleArray,
    @BsonProperty("a") val a: Double,
    @BsonProperty("b") val b: Double,
    @BsonProperty("rotate") val rotate: Double,
): MovementPath {
    override fun clone(): MovementPath {
        return copy()
    }
}