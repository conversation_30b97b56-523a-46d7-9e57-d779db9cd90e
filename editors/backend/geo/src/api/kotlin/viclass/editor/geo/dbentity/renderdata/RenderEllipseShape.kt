package viclass.editor.geo.dbentity.renderdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.NamePattern
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.buildEllipseRenderProp
import viclass.editor.geo.elements.Ellipse

@BsonDiscriminator(key = "renderElementType")
data class RenderEllipseShape @BsonCreator constructor(
    @BsonProperty("renderProp") override var renderProp: RenderProp,
    @BsonProperty("elType") override val elType: String = Ellipse::class.simpleName!!
) : RenderElement {

    @BsonProperty("elIndexes")
    override var elIndexes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("relIndex")
    override var relIndex: Int = -1

    @BsonProperty("name")
    override var name: String = ""

    @BsonProperty("area")
    var area: Double = 0.0

    @BsonProperty("perimeter")
    var perimeter: Double = 0.0

    @BsonProperty("usable")
    override var usable: Boolean = true

    @BsonProperty("deleted")
    override var deleted: Boolean? = null

    @BsonProperty("valid")
    override var valid: Boolean = true

    @BsonProperty("f1Idx")
    var f1Idx: Int = -1

    @BsonProperty("f2Idx")
    var f2Idx: Int = -1

    @BsonProperty("a")
    var a: Double = 0.0

    @BsonProperty("b")
    var b: Double = 0.0

    @BsonProperty("rotate")
    var rotate: Double = 0.0

    @BsonProperty("centerIdx")
    var centerIdx: Int = -1

    @BsonProperty("vaIdx")
    var vaIdx: Int = -1

    @BsonProperty("vbIdx")
    var vbIdx: Int = -1

    @BsonProperty("vertexRelIdxes")
    var vertexRelIdxes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("arcRelIdx")
    var arcRelIdx: Int = -1

    override fun clone(): RenderElement {
        return copy().also {
            cloneBase(it)
            it.area = area
            it.perimeter = perimeter
            it.f1Idx = f1Idx
            it.f2Idx = f2Idx
            it.a = a
            it.b = b
            it.rotate = rotate
            it.centerIdx = centerIdx
            it.vaIdx = vaIdx
            it.vbIdx = vbIdx
            it.vertexRelIdxes = vertexRelIdxes.toMutableSet()
            it.arcRelIdx = arcRelIdx
        }
    }

    override fun setDefaultProp(): RenderProp {
        renderProp = buildEllipseRenderProp(DocDefaultElRenderProp())
        return renderProp
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Ellipse::class, name)) return false
        return NamePattern.extractPointName(Ellipse::class, this.name).toSet() ==
                NamePattern.extractPointName(Ellipse::class, name).toSet()
    }
}