package viclass.editor.geo.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.solving.Coefficients

interface Ellipse: Parameterized, Element, AreaAble, PerimeterAble {
    val center: Vector3D
    val f1: Point
    val f2: Point
    val a: Double
    val b: Double
    val rotate: Double
    val coefs: Coefficients
    
    // Center-vector properties for ellipses created from center and vectors
    val vectorA: Point?
    val vectorB: Point?
    val centerPoint: Point?
    
    // Check if this ellipse has center-vector data
    fun hasCenterVectorData(): Boolean
}

