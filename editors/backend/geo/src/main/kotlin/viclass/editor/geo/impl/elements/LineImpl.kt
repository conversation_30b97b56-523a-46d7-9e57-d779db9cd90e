package viclass.editor.geo.impl.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.geometry.euclidean.threed.line.Line3D
import org.apache.commons.geometry.euclidean.threed.line.Lines3D
import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.constructor.DEFAULT_PRECISION
import viclass.editor.geo.math.MFunc
import kotlin.reflect.KClass

open class LineImpl constructor(
    override val doc: GeoDoc, override var name: String?,
    private var _p1: Point, private var _parallelVector: Vector3D, private var _p2: Point? = null
) : LineVi {
    override val clazz: KClass<out LineVi> = LineVi::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true

    override val p1: Point
        get() = _p1
    override val parallelVector: Vector3D
        get() = _parallelVector
    override val p2: Point?
        get() = _p2

    override fun line(): Line3D {
        val p = p1.coordinates()
        return Lines3D.fromPointAndDirection(p, parallelVector, DEFAULT_PRECISION)
    }

    override fun vertices(): List<Point> {
        return listOfNotNull(p1, p2)
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(LineVi::class, name)) return false
        if (this.name == name) return true
        return false
    }

    override fun length(): Double {
        return Double.MAX_VALUE
    }

    // TODO
    @Throws(UnsupportedOperationException::class)
    override fun parametricFunc(dim: Dimension): MFunc {
        throw UnsupportedOperationException()
    }

    override fun mergeFrom(other: Element) {
        if (other !is LineImpl) return
        super.mergeFrom(other)

        _parallelVector = other._parallelVector
    }

    override fun orderedVector(): Vector3D {
        return if (p2 != null) createVectorByEl(doc, p1, p2!!) else parallelVector
    }
}
