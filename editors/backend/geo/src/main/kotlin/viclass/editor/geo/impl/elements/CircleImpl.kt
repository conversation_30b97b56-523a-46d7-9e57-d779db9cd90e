package viclass.editor.geo.impl.elements

import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.math.MFunc
import kotlin.math.PI

class CircleImpl(
    override val doc: GeoDoc,
    override var name: String?,
    private var _centerPoint: Point,
    private var _radius: Double,
) : Circle {
    override val clazz = Circle::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true

    override val centerPoint: Point
        get() = _centerPoint
    override val radius: Double
        get() = _radius

    override fun parametricFunc(dim: Dimension): MFunc {
        TODO("Not yet implemented")
    }

    override fun vertices(): List<Point> {
        return listOf(centerPoint)
    }

    override fun area(): Double {
        return radius * radius * PI
    }

    override fun perimeter(): Double {
        return 2 * radius * PI
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Circle::class, name)) return false

        if (this.name == name) return true

        NamePattern[Circle::class]!![0].find(name)?.let {
            if (it.groupValues.size == 2) {
                val p1Name = it.groupValues[1]
                if ("($p1Name)" == this.name) return true
            }
            if (it.groupValues.size == 3) {
                val p1Name = it.groupValues[1]
                val p2Name = it.groupValues[2]

                if (p1Name == p2Name) // not a valid name
                    return false

                if ("($p1Name;$p2Name)" == this.name) return true
            }
        }

        return false
    }

    override fun mergeFrom(other: Element) {
        if (other !is Circle) return
        super.mergeFrom(other)

        _radius = other.radius
    }
}
