package viclass.editor.geo.impl.elements

import kotlin.math.PI
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt
import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.math.MFunc
import viclass.editor.geo.solving.Coefficients
import viclass.editor.geo.impl.utils.EllipseGeometryUtils

class EllipseImpl(
    override val doc: GeoDoc,
    override var name: String?,
    private var _a: Double,
    private var _b: Double,
    // Focus points - optional, calculated when needed
    private var _f1: Point? = null,
    private var _f2: Point? = null,
    // Center-vector properties - override directly from interface
    override val centerPoint: Point? = null,
    override val vectorA: Point? = null,
    override val vectorB: Point? = null
) : Ellipse {


    override val clazz = Ellipse::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true



    override val f1: Point
        get() {
            if (_f1 == null) {
                calculateFocusPoints()
            }
            return _f1!!
        }
    override val f2: Point
        get() {
            if (_f2 == null) {
                calculateFocusPoints()
            }
            return _f2!!
        }
    override val a: Double
        get() = _a
    override val b: Double
        get() = _b



    private lateinit var _vC: Vector3D
    private var _angle: Double = 0.0

    private lateinit var _coefs: Coefficients

    override val center: Vector3D
        get() = _vC
    override val rotate: Double
        get() = _angle

    /**
     * Các hệ số của phương trình bậc hai ẩn biểu diễn elip.
     *
     * Các hệ số này được tính toán trong hàm `validate()` và được sử dụng để xác định hình dạng và vị trí của elip
     * trong không gian.
     *
     * Các hệ số biểu diễn các thuật ngữ sau:
     * - AA: Hệ số của x^2
     * - BB: Hệ số của xy
     * - CC: Hệ số của y^2
     * - D: Hệ số của x
     * - E: Hệ số của y
     * - F: Hằng số
     */
    override val coefs: Coefficients
        get() {
            return _coefs
        }

    /**
     * Xác thực các thuộc tính của elip và tính toán các hệ số cho phương trình bậc hai ẩn.
     *
     * Hàm này xử lý cả hai mode tạo ellipse:
     * - FOCUS_POINTS: Sử dụng focus points để tính center và angle
     * - CENTER_VECTORS: Ưu tiên sử dụng center và vector points nếu có
     *
     * Sau khi xác định tâm và góc quay, hàm tính toán các hệ số của phương trình bậc hai ẩn của elip.
     */
    override fun validate() {
        if (hasCenterVectorData()) {
            _vC = centerPoint!!.coordinates()
            
            // Calculate angle and major axis vector using helper method
            val (angle, _) = calculateMajorAxisAndAngle()
            _angle = angle

            // CRITICAL FIX: Calculate focus points for center-vector mode
            calculateFocusPoints()

        } else {
            calculateFromFocusPoints()
        }

        // Tính toán các hệ số của phương trình bậc hai ẩn của elip.
        calculateCoefficients()
    }

    /**
     * Calculate major axis vector and angle from center-vector data
     * Returns Pair<angle, Pair<majorVecX, majorVecY>>
     * 
     * UPDATED: Calculate angle based on vector A direction, but adjust by 180° 
     * when vector B is on the opposite side (negative cross product).
     * This ensures ellipse orientation changes by 180° when B changes direction.
     */
    private fun calculateMajorAxisAndAngle(): Pair<Double, Pair<Double, Double>> {
        // check here
        val aVecX = vectorA!!.x - centerPoint!!.x
        val aVecY = vectorA!!.y - centerPoint!!.y
        val bVecX = vectorB!!.x - centerPoint!!.x
        val bVecY = vectorB!!.y - centerPoint!!.y

        val (aLength, bLength) = EllipseGeometryUtils.calculateAxisLengths(centerPoint!!, vectorA!!, vectorB!!)

        // Determine major axis vector for ellipse geometry calculations
        val majorVecX = if (aLength >= bLength) aVecX else bVecX
        val majorVecY = if (aLength >= bLength) aVecY else bVecY
        
        // Calculate base angle from vector A
        val angle = atan2(aVecY, aVecX)
        
        return Pair(angle, Pair(majorVecX, majorVecY))
    }

    /**
     * Calculate focus points from center and vector coordinates when needed
     * This enables lazy initialization of focus points for CENTER_VECTORS mode
     */
    private fun calculateFocusPoints() {
        if (hasCenterVectorData()) {
            // CENTER_VECTORS mode - calculate focus points from center and vectors
            val (_, majorVec) = calculateMajorAxisAndAngle()
            val (majorVecX, majorVecY) = majorVec

            // Calculate focal distance: c² = a² - b²
            val c = EllipseGeometryUtils.calculateFocalDistance(_a, _b)

            // Calculate focus points using utility
            val majorAxisVector = doubleArrayOf(majorVecX, majorVecY)
            val (f1, f2) = EllipseGeometryUtils.calculateFocusPoints(centerPoint!!, majorAxisVector, c)

            // Create focus points
            _f1 = PointImpl(doc, null, f1[0], f1[1])
            _f2 = PointImpl(doc, null, f2[0], f2[1])
        } else {
            // FOCUS_POINTS mode - focus points should already be set
            if (_f1 == null || _f2 == null) {
                throw IllegalStateException("Focus points not set for FOCUS_POINTS mode")
            }
        }
    }

    /**
     * Tính toán center và angle từ focus points (traditional method)
     */
    private fun calculateFromFocusPoints() {
        val focusPoint1 = _f1 ?: throw IllegalStateException("Focus point f1 is not available")
        val focusPoint2 = _f2 ?: throw IllegalStateException("Focus point f2 is not available")
        if (focusPoint1.coordinates() == focusPoint2.coordinates()) {
            _vC = focusPoint1.coordinates()
            _angle = 0.0
        } else {
            _vC = Vector3D.of(
                (focusPoint1.x + focusPoint2.x) / 2,
                (focusPoint1.y + focusPoint2.y) / 2,
                (focusPoint1.z + focusPoint2.z) / 2
            )
            val v0 = Vector3D.of(center.x + 10, center.y, center.z)
            val vecC0 = center.vectorTo(v0)
            val vecCE = center.vectorTo(focusPoint2.coordinates())
            _angle = vecC0.angleTo(vecCE)
        }
    }

    /**
     * Tính toán các hệ số của phương trình bậc hai ẩn của elip.
     */
    private fun calculateCoefficients() {
        val angle = -this._angle // Góc quay, phủ định.
        val cosAngle = cos(angle)
        val sinAngle = sin(angle)
        val centerX = center.x
        val centerY = center.y
        val aSquared = a * a // Bình phương trục bán kính lớn.
        val bSquared = b * b // Bình phương trục bán kính nhỏ.

        // Tính toán các hệ số của phương trình elip đã quay.
        val AA = cosAngle * cosAngle / aSquared + sinAngle * sinAngle / bSquared
        val BB = -2 * cosAngle * sinAngle / aSquared + 2 * cosAngle * sinAngle / bSquared
        val CC = sinAngle * sinAngle / aSquared + cosAngle * cosAngle / bSquared
        val D = -(2 * AA * centerX + BB * centerY)
        val E = -(BB * centerX + 2 * CC * centerY)
        val F = AA * centerX * centerX + BB * centerX * centerY + CC * centerY * centerY - 1

        // Lưu trữ các hệ số đã tính.
        _coefs = Coefficients(AA, BB, CC, D, E, F)
    }

    override fun parametricFunc(dim: Dimension): MFunc {
        TODO("Not yet implemented")
    }

    override fun vertices(): List<Point> {
        return listOf(f1, f2)
    }

    override fun area(): Double {
        return PI * a * b
    }

    override fun perimeter(): Double {
        return PI * (3 * (a + b) - sqrt((3 * a + b) * (a + 3 * b)))
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Ellipse::class, name)) return false
        return NamePattern.extractPointName(Ellipse::class, this.name!!).toSet() ==
                NamePattern.extractPointName(Ellipse::class, name).toSet()
    }

    override fun mergeFrom(other: Element) {
        if (other !is Ellipse) return
        super.mergeFrom(other)

        _a = other.a
        _b = other.b

        // Copy focus points if available
        if (other is EllipseImpl && other._f1 != null && other._f2 != null) {
            _f1 = other._f1
            _f2 = other._f2
        }

        // Recalculate center and angle based on available data
        if (hasCenterVectorData()) {
            _vC = centerPoint!!.coordinates()
            
            // Calculate angle using helper method
            val (angle, _) = calculateMajorAxisAndAngle()
            _angle = angle

            // CRITICAL FIX: Recalculate focus points after center-vector data changes
            calculateFocusPoints()
        } else {
            calculateFromFocusPoints()
        }

        // Recalculate coefficients after any changes
        calculateCoefficients()
    }

    /**
     * Utility method to check if ellipse has center-vector data
     */
    override fun hasCenterVectorData(): Boolean {
        return centerPoint != null && vectorA != null && vectorB != null
    }




    companion object {
        /**
         * Factory method: Create ellipse from focus points (traditional way)
         * Used for mode Points: f1, f2 (focus points) + a, b (major/minor radii)
         */
        fun fromFocusPoints(
            doc: GeoDoc,
            name: String?,
            f1: Point,
            f2: Point,
            a: Double,
            b: Double
        ): EllipseImpl {
            return EllipseImpl(
                doc = doc,
                name = name,
                _a = a,
                _b = b,
                _f1 = f1,
                _f2 = f2
            )
        }

        /**
         * Factory method: Create ellipse from center point and two axis vector points
         * Used for mode CenterVectors: center + vectorA + vectorB
         */
        fun fromCenterVectors(
            doc: GeoDoc,
            name: String?,
            center: Point,
            vectorA: Point,
            vectorB: Point
        ): EllipseImpl {
            // Calculate axis lengths using utility
            val (a, b) = EllipseGeometryUtils.calculateAxisLengths(center, vectorA, vectorB)

            // Create ellipse with center-vector mode - focus points will be calculated lazily
            return EllipseImpl(
                doc = doc,
                name = name,
                _a = a,
                _b = b,
                centerPoint = center,
                vectorA = vectorA,
                vectorB = vectorB
            )
        }
    }
}
