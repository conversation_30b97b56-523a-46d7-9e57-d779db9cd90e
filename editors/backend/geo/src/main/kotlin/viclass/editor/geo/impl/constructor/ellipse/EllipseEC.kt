package viclass.editor.geo.impl.constructor.ellipse

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithLengthTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.EllipseImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.PointOnLineWithLengthTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.impl.constructor.generateLowercaseName
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.utils.EllipseGeometryUtils
import kotlin.math.abs
import kotlin.math.sqrt
import kotlin.reflect.KClass

@Singleton
class EllipseEC() : ElementConstructor<Ellipse>, Logging {
    
    private enum class CGS { Points, CenterVectors }

    override fun outputType(): KClass<Ellipse> {
        return Ellipse::class
    }
    
    override fun template(): ConstructorTemplate {
        val cg1 =
            ConstraintGroupBuilder.create()
                .name(CGS.Points.name)
                .hints()
                .constraint(
                    0,
                    ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                    listOf("NameOfPoint", "NameOfPoint", "NameOfPoint"),
                    "tpl-EllipseFocusPoints"
                )
                .build()

        val cg2 =
            ConstraintGroupBuilder.create()
                .name(CGS.CenterVectors.name)
                .hints()
                .constraint(
                    0,
                    ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                    listOf("NameOfPoint", "NameOfPoint"),
                    "tpl-CenterVectors"
                )
                .constraint(
                    1,
                    ConstraintParamDefManager.instance()[ConstraintParamDefManager.aValue]!!,
                    listOf("SemiMinorAxisLength"),
                    "tpl-SemiMinorAxisLength"
                )
                .constraintOptional(
                    2,
                    ConstraintParamDefManager.instance()[ConstraintParamDefManager.aValue]!!,
                    listOf(0, 1),
                    listOf("NthResult"),
                    "tpl-NthResult"
                )
                .constraintOptional(
                    3,
                    ConstraintParamDefManager.instance()["aName"]!!,
                    listOf(0, 1, 2),
                    listOf("NameOfPoint"),
                    "tpl-EndPoint"
                )
                .build()

        return ConstructorTemplateBuilder.create(this).cgs(cg1, cg2).elTypes(Ellipse::class).build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Ellipse> {
        return when (val cg = CGS.valueOf(c.cgName)) {
            CGS.Points -> {
                Validations.validateNumConstraints(c, 1)
                constructEllipse(doc, inputName, c, cg)
            }
            CGS.CenterVectors -> {   Validations.validateNumConstraints(c, 3)
                constructEllipseCenterVectors(doc, inputName, c, cg)
            }
        }
    }

    private fun constructEllipse(doc: GeoDoc, name: String?, cst: Construction, cg: CGS): ConstructionResult<Ellipse> {
        val pointsER =
            extractFirstPossible<ElementListExtraction<Point>>(doc, ParamKind.PK_Name, cst.params[0], cst.ctIdx)
        val f1 = pointsER.result[0].result() ?: throw ElementNotExistInDocumentException("not found vertex")
        val f2 = pointsER.result[1].result() ?: throw ElementNotExistInDocumentException("not found vertex")
        val f3 = pointsER.result[2].result() ?: throw ElementNotExistInDocumentException("not found vertex")

        // Calculate distance between foci
        val c = Distances.of(f1, f2) / 2

        // Distance between f3 and each focus
        val d1 = sqrt((f3.x - f1.x) * (f3.x - f1.x) + (f3.y - f1.y) * (f3.y - f1.y))
        val d2 = sqrt((f3.x - f2.x) * (f3.x - f2.x) + (f3.y - f2.y) * (f3.y - f2.y))

        // Sum of distances from f3 to foci is 2a
        val a = (d1 + d2) / 2

        // Calculate b using a^2 = b^2 + c^2
        val b = sqrt(a * a - c * c)

        val cr = ConstructionResultImpl<Ellipse>()
        val ellipse = EllipseImpl.fromFocusPoints(doc, name ?: generateLowercaseName(doc,arrayListOf()), f1, f2, a, b)
        cr.setResult(ellipse)
        cr.addDependency(f1, emptyList(), true)
        cr.addDependency(f2, emptyList(), true)
        cr.addDependency(f3, emptyList(), true)

        return cr
    }

    /**
     * Construct an ellipse from center point, pA point, and semi-minor axis length
     * Creates new pB point internally like IsoscelesTriangleEC creates apex point
     * @param doc GeoDoc
     * @param name desired name, nullable
     * @param cst Construction parameters (2 points + 2 values: length + nth)
     * @param cg constraint group
     */
    private fun constructEllipseCenterVectors(
        doc: GeoDoc,
        name: String?,
        cst: Construction,
        cg: CGS
    ): ConstructionResult<Ellipse> {
        // Extract input parameters
        val pointsER = extractFirstPossible<ElementListExtraction<Point>>(doc, ParamKind.PK_Name, cst.params[0], cst.ctIdx)
        val center = pointsER.result[0].result() ?: throw ElementNotExistInDocumentException("Center point not found")
        val pA = pointsER.result[1].result() ?: throw ElementNotExistInDocumentException("pA point not found")
        val semiMinorAxisLength = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, cst.params[1], cst.ctIdx).result
        
        // Extract nth parameter (frontend ensures valid values)
        val nthValue = extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, cst.params[2], cst.ctIdx).result
        val nthResult = nthValue - 1 // Convert to 0-based index
        
        // Extract pB point name
        val pBPointName = cst.params.find { it.paramDef.id == "aName" }
            ?.let { extractFirstPossible<StringExtraction>(doc, ParamKind.PK_Name, it, cst.ctIdx).result }
        val pBName = if (pBPointName.isNullOrBlank()) generatePointName(doc) else pBPointName
        
        val ellipseName = name ?: generateLowercaseName(doc, arrayListOf())
        
        // Calculate ellipse parameters from center vectors
        val (a, b, majorAxisVector) = EllipseGeometryUtils.calculateEllipseParametersFromCenterVectors(
            center, pA, semiMinorAxisLength
        )
        
        // Calculate minor axis direction (perpendicular to major axis)
        val minorAxisUnitVector = EllipseGeometryUtils.calculatePerpendicularUnitVector(majorAxisVector)
        
        // Calculate the two possible positions for pB
        val (option1, option2) = EllipseGeometryUtils.calculatePerpendicularPositions(
            center, semiMinorAxisLength, minorAxisUnitVector
        )
        
        // Choose the appropriate option based on nthResult
        val pBCoords = if (nthResult == 0) option1 else option2
        
        // Validate ellipse parameters and construction
        EllipseGeometryUtils.validateEllipseParameters(a, b)
        EllipseGeometryUtils.validateEllipseConstruction(
            center, pA, pBCoords, majorAxisVector, a, b
        )
        
        // Create point B with transformation capabilities
        val pB = EllipseGeometryUtils.createTransformablePoint(
            doc, pBName, pBCoords, center, minorAxisUnitVector, 1, 2
        )
        
        // Create ellipse from center vectors
        val ellipse = EllipseImpl.fromCenterVectors(doc, ellipseName, center, pA, pB)

        return ConstructionResultImpl<Ellipse>().apply {
             setResult(ellipse)
             addDependency(center, emptyList(), true)
             addDependency(pA, emptyList(), true)
             addDependency(ellipse.vectorB!!, listOf(center, pA), true)
         }
    }
}
