package viclass.editor.geo.impl.constructor.angle

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLine
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Angle
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generateLowercaseName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.elements.AngleImpl
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class AngleEC constructor(
    val extractorManager: ParamExtractorManager,
) : ElementConstructor<Angle> {

    override fun outputType(): KClass<Angle> {
        return Angle::class
    }

    private enum class CGS {
        FromLinesAndDirection,
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this).cgs(
            ConstraintGroupBuilder.create().name(CGS.FromLinesAndDirection.name).constraint(
                0, ConstraintParamDefManager.instance()[aLine]!!, listOf("Line"), "tpl-LineStart"
            ).constraint(
                1, ConstraintParamDefManager.instance()[aLine]!!, listOf("Line"), "tpl-LineEnd"
            ).constraint(
                2, ConstraintParamDefManager.instance()[aPoint]!!, listOf("NameOfPoint"), "tpl-IntersectionOfLine"
            ).constraintDepends(
                3,
                ConstraintParamDefManager.instance()[aValue]!!,
                listOf(0),
                listOf("Value"),
                "tpl-DirectionOfLineStart"
            ).constraintDepends(
                4, ConstraintParamDefManager.instance()[aValue]!!, listOf(1), listOf("Value"), "tpl-DirectionOfLineEnd"
            ).build(),
        ).elTypes(Angle::class).build()
    }

    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<Angle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromLinesAndDirection -> {
                Validations.validateNumConstraints(c, 5)
                constructFromLineAndDirection(doc, inputName, c)
            }
        }
    }

    private fun constructFromLineAndDirection(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<Angle> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr5: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[2], c.ctIdx)
        val exr3: NumberExtraction<Int> = extractFirstPossible(doc, ParamKind.PK_Value, c.params[3], c.ctIdx)
        val exr4: NumberExtraction<Int> = extractFirstPossible(doc, ParamKind.PK_Value, c.params[4], c.ctIdx)

        val lineStart: LineVi = exr1.result.result()!!
        val lineEnd: LineVi = exr2.result.result()!!
        val directionStart: Int = exr3.result
        val directionEnd: Int = exr4.result
        val intersectionPoint: Point = exr5.result.result()!!

        // validate the inputName
        val inputPointNames = NamePattern.extractPointName(Angle::class, inputName.toString())
        if (inputPointNames.size != 1 || !inputPointNames[0][0].isLowerCase()) throw IllegalArgumentException(
            "The input name for angle must be a single lowercase name, but got: $inputName"
        )

        val angleName = inputName ?: generateLowercaseName(doc, mutableListOf())
        val angle: Angle =
            AngleImpl(doc, angleName, intersectionPoint, lineStart, directionStart, lineEnd, directionEnd)

        val cr = ConstructionResultImpl<Angle>()
        cr.setResult(angle)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.mergeAsDependency(exr5.result)

        return cr
    }
}
