package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Angle
import viclass.editor.geo.elements.Element
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import kotlin.reflect.KClass

/**
 * A Angle extractor extract Angles from given names. The name should have follow conventional format
 *
 * <AUTHOR>
 */
@Singleton
class AngleExtractor constructor(private val evaluator: Evaluator) : ParameterExtractor {

    override var id: String = javaClass.simpleName

    @Throws(ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(
        doc: GeoDoc, pk: String, ctIdx: Int, clazz: KClass<out T>?
    ): ConstructionResult<T> {
        val shapeName = evaluator.extractRootOperator(pk) { extractElementName(it, listOf("Hinh", "Goc")) }
            ?: throw ElementNotExistInDocumentException("Invalid expression: $pk")

        // Try direct match first
        doc.findElementByName(shapeName, Angle::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Angle>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        val inputPointNames = NamePattern.extractPointName(Angle::class, shapeName)
        doc.findElementByName(shapeName, Angle::class, ctIdx, null, { el ->
            val elPointNames = NamePattern.extractPointName(Angle::class, el.name.toString())

            if (elPointNames.size == 1 && elPointNames[0][0].isLowerCase()) // if the element name is an angle name
                if ((inputPointNames.size == 3 && inputPointNames[1] == el.anglePoint.name && el.lineStart.vertices()
                        .indexOfFirst { p -> p.name == inputPointNames[0] } >= 0 && el.lineEnd.vertices()
                        .indexOfFirst { p -> p.name == inputPointNames[2] } >= 0) // if the input name is list of 3 points, then we can check if the angle is constructed from these 3 points
                    || inputPointNames.size == 1 && inputPointNames[0][0].isUpperCase() && inputPointNames[0] == el.anglePoint.name // if the input name is a single point, then we can check if the angle is constructed from this point
                ) return@findElementByName shapeName

            return@findElementByName el.name.toString()
        })?.let {
            val cr = ConstructionResultImpl<Angle>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        throw ElementNotExistInDocumentException("No Angle with name $shapeName could be found or constructed")
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(Angle::class)
    }
}
