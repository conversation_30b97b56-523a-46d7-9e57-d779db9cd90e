package viclass.editor.geo.impl.constructor.line

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithCoefficientTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Angle
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Ray
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.RayImpl
import viclass.editor.geo.impl.transformer.PointOnLineWithCoefficientTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.math.sqrt
import kotlin.reflect.KClass

/**
 * This class implements the construction logic for the angle bisector and its related geometric objects.
 * It provides methods to construct a bisector ray, a bisector segment of given length, and a bisector segment that intersects another line.
 *
 * <AUTHOR>
 */
@Singleton
class BisectorOfAngleEC constructor() : ElementConstructor<LineVi> {

    // Enum for the different construction group scenarios supported by this class
    private enum class CGS {
        BisectorAngle, // Construct a bisector ray
        BisectorAngleSegment, // Construct a bisector segment of given length
        BisectorAngleSegmentWithIntersectionLine // Construct a bisector segment that intersects a given line
    }

    // Returns the template describing the construction groups and their parameters
    override fun template(): ConstructorTemplate {
        val cg1 = ConstraintGroupBuilder.create().name(CGS.BisectorAngle.name).hints("hint-BisectorOfAngle").constraint(
            0,
            ConstraintParamDefManager.instance()[ConstraintParamDefManager.anAngle]!!,
            listOf("NameOfAngle"),
            "tpl-BisectorOfAngle"
        ).constraint(
            1,
            ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
            listOf("NameOfPoint"),
            "tpl-Point"
        ).build()

        return ConstructorTemplateBuilder.create(this).cgs(cg1).elTypes(LineVi::class, LineSegment::class, Ray::class)
            .build()
    }

    // Main entry point for constructing the bisector or related objects based on the construction group
    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.BisectorAngle -> {
                Validations.validateNumConstraints(c, 1)
                constructBisectorAngle(doc, inputName, c)
            }

            CGS.BisectorAngleSegment -> {
                Validations.validateNumConstraints(c, 2)
                constructBisectorAngleSegment(doc, inputName, c)
            }

            CGS.BisectorAngleSegmentWithIntersectionLine -> {
                Validations.validateNumConstraints(c, 2)
                constructBisectorAngleSegmentWithIntersectionLine(doc, inputName, c)
            }
        }
    }

    // Helper function to calculate the bisector vector given two vectors representing the angle's sides
    private fun calculateBiSectorVectorOfAngle(a: List<Double>, b: List<Double>): List<Double> {
        // Calculate the dot product of a and b
        val dotProduct = a[0] * b[0] + a[1] * b[1]

        // Magnitude of vector a
        val magnitudeA = sqrt(a[0] * a[0] + a[1] * a[1])

        // Magnitude of vector b
        val magnitudeB = sqrt(b[0] * b[0] + b[1] * b[1])

        // Calculate the cosine of the angle between a and b
        val cosTheta = dotProduct / (magnitudeA * magnitudeB)

        // Calculate the sine of the angle between a and b
        val sinTheta = sqrt(1 - cosTheta * cosTheta)

        // Calculate the x and y components of the angle bisector vector
        val cX = (magnitudeA * b[1] - magnitudeB * a[1]) / (2 * sinTheta)
        val cY = (magnitudeB * a[0] - magnitudeA * b[0]) / (2 * sinTheta)

        return listOf(cX, cY)
    }

    // Helper function to find the coordinates of a point at a distance k along a given vector from a starting point
    private fun findEndingPoint(
        sPointX: Double, sPointY: Double, uVectorX: Double, uVectorY: Double, k: Double
    ): List<Double> {
        val x = sPointX + uVectorX * k;
        val y = sPointY + uVectorY * k;
        return listOf(x, y);
    }

    // Constructs a bisector ray for a given angle
    private fun constructBisectorAngle(doc: GeoDoc, name: String?, c: Construction): ConstructionResult<LineVi> {
        // Extract the angle element from the document
        val exr: ElementExtraction<Angle> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val angle = exr.result.result()!!

        // Get the vectors and directions for the angle's sides
        val lsVector = angle.lineStart.parallelVector
        val leVector = angle.lineEnd.parallelVector
        val ds = angle.directionStart
        val de = angle.directionEnd

        // Calculate the bisector vector
        val bisectorVectorArr = calculateBiSectorVectorOfAngle(
            listOf(ds * lsVector.x, ds * lsVector.y), listOf(de * leVector.x, de * leVector.y)
        )
        val bisectorVector = Vector3D.of(bisectorVectorArr[0], bisectorVectorArr[1], 0.0)

        // Create a new ray starting at the angle's vertex and following the bisector direction
        val rayName = name ?: generateRayName(doc, angle.anglePoint.name.toString())
        val bisectorLine = RayImpl(doc, rayName, angle.anglePoint, bisectorVector)

        // Prepare the construction result
        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(bisectorLine)
        cr.mergeAsDependency(exr.result)

        return cr
    }

    // Constructs a bisector segment of a given length for a given angle
    private fun constructBisectorAngleSegment(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        // Extract the angle and the length parameter
        val exr1 = extractFirstPossible<ElementExtraction<Angle>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2 = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, c.params[1], c.ctIdx)

        val angle = exr1.result.result() ?: throw ElementNotExistInDocumentException("The angle does not exist")
        val k = exr2.result

        // Get the vectors and directions for the angle's sides
        val lsVector = angle.lineStart.parallelVector
        val leVector = angle.lineEnd.parallelVector
        val ds = angle.directionStart
        val de = angle.directionEnd

        // Calculate the bisector vector
        val bisectorVectorArr = calculateBiSectorVectorOfAngle(
            listOf(ds * lsVector.x, ds * lsVector.y), listOf(de * leVector.x, de * leVector.y)
        )
        val bisectorVector = Vector3D.of(bisectorVectorArr[0], bisectorVectorArr[1], 0.0)

        val endPointNameExtr = (inputName?.let {
            NamePattern.extractPointName(LineSegment::class, inputName) - setOf(angle.anglePoint.name)
        } ?: emptySet())
        if (endPointNameExtr.isEmpty() || endPointNameExtr.size > 1) throw ConstructionException("Invalid input name for the bisector segment")
        val endPointName = endPointNameExtr.first()

        // Generate a new point at distance k along the bisector
        val vu = bisectorVector.normalize()
        val coords = this.findEndingPoint(
            angle.anglePoint.coordinates().x, angle.anglePoint.coordinates().y, vu.x, vu.y, k
        )

        // Create the new point and set its transformation and movement data
        val newPoint = PointImpl(doc, endPointName, coords[0], coords[1])
        newPoint.transformer = TransformMapping.fromClazz(PointOnLineWithCoefficientTransformer::class)
        newPoint.transformData = PointOnLineWithCoefficientTransformData(
            targetParamIdx = 1,
            paramKind = ParamKind.PK_Value,
            rootPoint = angle.anglePoint.coordinates().toArray(),
            unitVector = vu.toArray()
        )
        newPoint.movementPath = MovementLinePath(angle.anglePoint.coordinates().toArray(), vu.toArray())

        // Create the bisector segment from the angle's vertex to the new point
        val bisectorLine = LineSegmentImpl(doc, "${angle.anglePoint.name}${newPoint.name}", angle.anglePoint, newPoint)

        // Prepare the construction result
        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(bisectorLine)
        cr.mergeAsDependency(exr1.result)
        cr.addDependency(newPoint, listOf(angle), true)

        return cr
    }

    // Constructs a bisector segment that ends at the intersection with another line
    private fun constructBisectorAngleSegmentWithIntersectionLine(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        // Extract the angle and the intersection line
        val exr1: ElementExtraction<Angle> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)

        val angle = exr1.result.result() ?: throw ElementNotExistInDocumentException("The angle does not exist")
        val intersectionLine =
            exr2.result.result() ?: throw ElementNotExistInDocumentException("The intersection line does not exist")

        // Get the vectors and directions for the angle's sides
        val lsVector = angle.lineStart.parallelVector
        val leVector = angle.lineEnd.parallelVector
        val ds = angle.directionStart
        val de = angle.directionEnd

        // Calculate the bisector vector
        val bisectorVectorArr = calculateBiSectorVectorOfAngle(
            listOf(ds * lsVector.x, ds * lsVector.y), listOf(de * leVector.x, de * leVector.y)
        )
        val bisectorVector = Vector3D.of(bisectorVectorArr[0], bisectorVectorArr[1], 0.0)

        // Determine the name for the intersection point
        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(angle.anglePoint.name)
        }?.first() ?: generatePointName(doc)
        // Create a temporary line along the bisector
        val tempLine = LineImpl(doc, null, angle.anglePoint, bisectorVector)
        // Find the intersection point between the bisector and the given line
        val intersectionPoint =
            Intersections.of(intersectionLine, tempLine) ?: throw ConstructionException("Invalid angle")

        // Create the intersection point
        val newPoint = PointImpl(doc, pointName, intersectionPoint.x, intersectionPoint.y)

        // Create the bisector segment from the angle's vertex to the intersection point
        val newLine = LineSegmentImpl(
            doc, "${angle.anglePoint.name}${newPoint.name}", angle.anglePoint, newPoint
        )

        // Prepare the construction result
        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.addDependency(newPoint, listOf(angle, intersectionLine), true)

        return cr
    }

    // Returns the output type of this constructor (LineVi)
    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }
}
