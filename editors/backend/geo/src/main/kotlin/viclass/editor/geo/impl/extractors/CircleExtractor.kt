package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Element
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import kotlin.reflect.KClass

/**
 * A circle extractor extract circles from given names. The name should have follow conventional format
 *
 * <AUTHOR>
 */
@Singleton
class CircleExtractor constructor(private val evaluator: Evaluator) : ParameterExtractor {

    override var id: String = javaClass.simpleName

    @Throws(ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(
        doc: GeoDoc,
        pk: String,
        ctIdx: Int,
        clazz: KClass<out T>?
    ): ConstructionResult<T> {
        val shapeName = evaluator.extractRootOperator(pk) {
            extractElementName(it, listOf("Hinh", "HinhTron"))
        } ?: throw ElementNotExistInDocumentException("Invalid expression: $pk")

        doc.findElementByName(shapeName, Circle::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Circle>()
            cr.setResult(it)
            cr.newly = false
            @Suppress("UNCHECKED_CAST")
            return cr as ConstructionResult<T>
        }

        val matched = doc.elements
            .filterIsInstance<Circle>()
            .filter {
                it.centerPoint.name == shapeName &&
                        it.valid &&
                        it.usable &&
                        it.deleted != true
            }

        return when (matched.size) {
            1 -> {
                val cr = ConstructionResultImpl<Circle>()
                cr.setResult(matched.first())
                cr.newly = false
                @Suppress("UNCHECKED_CAST")
                cr as ConstructionResult<T>
            }

            0 -> throw ElementNotExistInDocumentException(
                "No circle with center point name '$shapeName' could be found or constructed."
            )

            else -> throw ElementNotExistInDocumentException(
                "Cannot proceed: multiple circles found with the same center point name '$shapeName'. Expected only one."
            )
        }
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(Circle::class)
    }
}
