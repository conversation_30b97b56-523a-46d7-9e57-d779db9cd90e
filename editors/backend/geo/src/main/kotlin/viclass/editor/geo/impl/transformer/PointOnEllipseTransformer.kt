package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnEllipseTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.transformer.Transformer

class PointOnEllipseTransformer constructor(): Transformer<PointOnEllipseTransformData> {
    override fun apply(
        doc: GeoDoc,
        c: Construction,
        transformData: PointOnEllipseTransformData,
        pos: Vector3D
    ) {
        val vC = Vector3D.of(transformData.center[0], transformData.center[1], 0.0)
        val vS = Vector3D.of(transformData.f2[0], transformData.f2[1], 0.0)
        val vec = vC.vectorTo(pos)
        val vecS = vC.vectorTo(vS)
        val angle = vecS.angleTo(vec)
        
        val ps = c.params[transformData.targetParamIdx].specs
            .getParam(transformData.paramKind) as ParamStoreValue
        ps.value = angle.toString()
    }
}