import {
    <PERSON>E<PERSON>,
    CoordinatorEventType,
    DefaultToolBar,
    KeyboardEventData,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventListener,
    NativeEventTarget,
    PointerEventListener,
    ProxyObject,
    ToolEventListener,
    ToolState,
    VEventListener,
    ViewportDisableCES,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';

import { GeometryEditor } from './geo.editor';
import {
    CommonToolState,
    GeometryDocEvent,
    GeometryDocFocusedES,
    GeoPanToolState,
    GeoZoomToolState,
    InputCommandToolStateImpl,
    ListElementToolState,
    NamingElementToolState,
    QuadToolState,
    RegularPolygonToolState,
    RenameElementToolState,
    SectorToolState,
    TriangleToolState,
    EllipseToolState,
    UpdatePropToolState,
} from './model';
import { GeometryToolType, GeoObjectType, GeoToolEventData } from './model/geo.models';
import { GeometryTool } from './tools';

export class GeometryToolBar extends DefaultToolBar<GeometryToolType, GeometryTool<ToolState>> {
    override keyboardHandler?: KeyboardEventListener<NativeEventTarget<any>>;
    override mouseHandler?: MouseEventListener<NativeEventTarget<any>>;
    override pointerHandler?: PointerEventListener<NativeEventTarget<any>>;
    toolListener: ToolEventListener<GeometryToolBar, any>;
    geoDocEventListener: VEventListener<GeometryDocEvent>;

    constructor(
        public editor: GeometryEditor,
        objTypeStr: Map<GeoObjectType, string>
    ) {
        super(editor.coordinator);
        this.initializeToolbarState(objTypeStr);
    }

    protected override async onViewportModeChanged(vpMode: ViewportMode): Promise<void> {
        if (vpMode == 'Disabled') this.disable();
        else this.enable();

        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                case 'InteractiveMode': {
                    switch (type) {
                        case 'ListElementTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                            break;
                        }
                    }
                    break;
                }
                case 'ViewMode': {
                    switch (type) {
                        case 'ListElementTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                            break;
                        }
                    }
                    break;
                }
                case 'Disabled': {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    private initializeToolbarState(objTypeStr: Map<GeoObjectType, string>) {
        const commonToolState = new CommonToolState();
        this.toolbarsState
            .set('CommonPropertiesTool', commonToolState)

            .set('InputCommandTool', new ProxyObject(new InputCommandToolStateImpl(objTypeStr)).proxyInstance)
            .set('UpdatePropTool', new ProxyObject(new UpdatePropToolState()).proxyInstance)
            .set('RenameElementTool', new ProxyObject(new RenameElementToolState()).proxyInstance)
            .set('NamingElementTool', new ProxyObject(new NamingElementToolState()).proxyInstance)
            .set('ListElementTool', new ProxyObject(new ListElementToolState()).proxyInstance)

            .set('CreatePointTool', commonToolState)
            .set('PointOnObjectTool', commonToolState)
            .set('IntersectionPointTool', commonToolState)
            .set('MiddlePointTool', commonToolState)

            .set('CreateVectorTool', commonToolState)
            .set('CreateRayTool', commonToolState)
            .set('CreateLineSegmentTool', commonToolState)
            .set('CreateLineTool', commonToolState)

            .set('CreateCircleTool', commonToolState)
            .set('CreateEllipseTool', new EllipseToolState())
            .set('CreateSemicircleTool', commonToolState)
            .set('CreateSectorTool', new SectorToolState())

            .set('CreateTriangleTool', commonToolState)
            .set('CreateEquilateralTriangleTool', commonToolState)
            .set('CreateIsoscelesTriangleTool', new TriangleToolState())
            .set('CreateIsoscelesRightTriangleTool', new TriangleToolState())
            .set('CreateRightTriangleTool', new TriangleToolState())

            .set('CreatePolygonTool', commonToolState)
            .set('CreateRegularPolygonTool', new RegularPolygonToolState())
            .set('CreateSquareTool', new QuadToolState())
            .set('CreateRectangleTool', new QuadToolState())
            .set('CreateParallelogramTool', new QuadToolState())
            .set('CreateRhombusTool', new QuadToolState())
            .set('CreateTrapezoidTool', new QuadToolState())

            .set('CreateAngleTool', commonToolState)
            .set('CreateAngleByThreePointsTool', commonToolState)
            .set('CreatePerpendicularLineTool', commonToolState)
            .set('CreateParallelLineTool', commonToolState)
            .set('CreateBisectorLineTool', commonToolState)
            .set('CreateSymmetricThroughLineTool', commonToolState)
            .set('CreateSymmetricThroughPointTool', commonToolState);

        this.toolbarsState.set('GeoPanTool', new GeoPanToolState());
        this.toolbarsState.set('GeoZoomTool', new GeoZoomToolState());

        this.toolListener = new this._toolListener(this);

        this.keyboardHandler = new this._keyboardHandler(this);
        this.keyboardHandling.push(
            new (class extends KeyboardHandlingItem {
                override global = false;
            })(['esc'])
        );
    }

    override attachViewport(viewport: ViewportManager): void {
        super.attachViewport(viewport);
        this.geoDocEventListener = new this._geoDocEventListener(this);
        this.editor.selectDelegator.registerDocEventListener(this.geoDocEventListener);
        this.registerToolListener(this.toolListener);
    }

    override detachViewport(viewport: ViewportManager): void {
        super.detachViewport(viewport);
        if (this.geoDocEventListener) this.editor.selectDelegator.unregisterDocEventListener(this.geoDocEventListener);
        this.unregisterToolListener(this.toolListener);
    }

    private _toolListener = class implements ToolEventListener<GeometryToolBar, any> {
        constructor(private toolbar: GeometryToolBar) {}
        onEvent(eventData: GeoToolEventData): GeoToolEventData | Promise<GeoToolEventData> {
            if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(eventData.toolType)) return eventData;
            const tool = eventData.source.getTool(eventData.toolType);
            return tool ? tool.handleToolEvent(eventData) : eventData;
        }
    };

    protected override generateCoordEventListener(): VEventListener<CoordinatorEvent> {
        return new (class implements VEventListener<CoordinatorEvent> {
            constructor(private toolbar: GeometryToolBar) {}

            onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
                const eType = eventData.eventType as CoordinatorEventType;
                const eventState = eventData.state;

                if (!this.toolbar.viewport || eventState.vmId != this.toolbar.viewport.id) return eventData;

                switch (eType) {
                    case 'viewport-edit-mode': {
                        this.toolbar.onViewportModeChanged('EditMode');
                        break;
                    }
                    case 'viewport-interactive-mode': {
                        this.toolbar.onViewportModeChanged('InteractiveMode');
                        break;
                    }
                    case 'viewport-view-mode': {
                        this.toolbar.onViewportModeChanged('ViewMode');
                        break;
                    }
                    case 'viewport-disabled': {
                        const evs = eventState as ViewportDisableCES;
                        this.toolbar.editor.selectDelegator.onViewportDisabled(evs.vmId, evs);
                        this.toolbar.onViewportModeChanged('Disabled');
                        break;
                    }
                    case 'viewport-removed': {
                        this.toolbar.editor.selectDelegator.onViewportRemoved(eventData.state.vmId);
                        break;
                    }
                }

                return eventData;
            }
        })(this);
    }

    private _geoDocEventListener = class implements VEventListener<GeometryDocEvent> {
        get editor() {
            return this.toolbar.editor;
        }

        constructor(private toolbar: GeometryToolBar) {}

        onEvent(eventData: GeometryDocEvent): GeometryDocEvent | Promise<GeometryDocEvent> {
            if (this.toolbar.isDisabled()) return eventData;

            if (!this.toolbar.viewport || eventData.state.vm.id != this.toolbar.viewport.id) return eventData;

            switch (eventData.eventType) {
                case 'doc-focused': {
                    const { vm, docCtrl } = eventData.state as GeometryDocFocusedES;
                    this.editor.selectDelegator
                        .getFocusedDocs(vm.id)
                        .forEach(_docCtrl => _docCtrl.updateSelectedElements([]));

                    docCtrl.select();

                    this.toolbar.blur('CreateDocumentTool');
                    break;
                }
                case 'doc-unfocused': {
                    const { docCtrl } = eventData.state as GeometryDocFocusedES;
                    if (docCtrl.selectedElements?.length) this.editor.clearSelectedElInDoc(docCtrl);
                    docCtrl.unselect();
                    break;
                }
            }

            return eventData;
        }
    };

    private _keyboardHandler = class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(private _p: GeometryToolBar) {}

        onEvent(event: KeyboardEventData<NativeEventTarget<any>>): KeyboardEventData<NativeEventTarget<any>> {
            if (this._p.isDisabled() || event.nativeEvent.repeat) return event;
            if (event.nativeEvent.key === 'Escape' && this._p.curTool) {
                this._p.blur(this._p.curTool);
            }

            return event;
        }
    };
}
