export declare type MovementPathType =
    | 'MovementFreePath'
    | 'MovementLinePath'
    | 'MovementLineSegmentPath'
    | 'MovementSectorPath'
    | 'MovementCirclePath'
    | 'MovementEllipsePath';
export interface MovementPath {
    type: MovementPathType;
}
export class MovementFreePath implements MovementPath {
    type: MovementPathType = 'MovementFreePath';
}
export class Movement<PERSON>inePath implements MovementPath {
    type: MovementPathType = 'MovementLinePath';
    root: number[];
    parallelVector: number[];
}
export class MovementLineSegmentPath implements MovementPath {
    type: MovementPathType = 'MovementLineSegmentPath';
    p1: number[];
    p2: number[];
}
export class MovementSectorPath implements MovementPath {
    type: MovementPathType = 'MovementSectorPath';
    pc: number[];
    ps: number[];
    pe: number[];
}
export class MovementCirclePath implements MovementPath {
    type: MovementPathType = 'MovementCirclePath';
    pc: number[];
    radius: number;
}
export class MovementEllipsePath implements MovementPath {
    type: MovementPathType = 'MovementEllipsePath';
    center: number[];
    f1: number[];
    f2: number[];
    a: number;
    b: number;
    rotate: number;
}
