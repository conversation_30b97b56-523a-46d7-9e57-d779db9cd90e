import { point } from '@flatten-js/core';

/**
 * Calculate ellipse geometry from focus points and optional parameters
 */
export function calcEllipseGeometry(
    f1: number[],
    f2: number[],
    pointOnEllipse?: number[],
    existingA?: number,
    existingB?: number
): {
    center: [number, number];
    rotation: { angle: number; cos: number; sin: number };
    a?: number;
    b?: number;
} {
    // Center calculation
    const cx = (f1[0] + f2[0]) / 2;
    const cy = (f1[1] + f2[1]) / 2;

    // Rotation angle (major axis direction) - from center to f1 (standard convention)
    const rotationAngle = Math.atan2(f1[1] - cy, f1[0] - cx);
    const cos = Math.cos(rotationAngle);
    const sin = Math.sin(rotationAngle);

    let a = existingA;
    let b = existingB;

    // Calculate a, b from 3 points if not provided
    if ((a == null || b == null) && pointOnEllipse) {
        const f1p = point(f1[0], f1[1]);
        const f2p = point(f2[0], f2[1]);
        const p3p = point(pointOnEllipse[0], pointOnEllipse[1]);

        const c = f1p.distanceTo(f2p)[0] / 2; // Half distance between foci
        const d1 = p3p.distanceTo(f1p)[0]; // Distance from pointOnEllipse to f1
        const d2 = p3p.distanceTo(f2p)[0]; // Distance from pointOnEllipse to f2

        a = (d1 + d2) / 2; // Semi-major axis
        b = Math.sqrt(a * a - c * c); // Semi-minor axis
    }

    return {
        center: [cx, cy] as [number, number],
        rotation: { angle: rotationAngle, cos, sin },
        a,
        b,
    };
}

/**
 * Calculate ellipse vertices (center, vertexA, vertexB) from geometry
 */
export function calcEllipseVertices(center: number[], a: number, b: number, cos: number, sin: number) {
    // Calculate vertices on major and minor axes
    const vertexA = [center[0] + a * cos, center[1] + a * sin]; // Vertex on major axis
    const vertexB = [center[0] - b * sin, center[1] + b * cos]; // Vertex on minor axis

    return {
        center: center,
        vertexA: vertexA,
        vertexB: vertexB,
    };
}

/**
 * Calculate ellipse geometry from center and two vectors
 */
export function calcEllipseFromVectors(
    center: number[],
    vectorA: number[],
    vectorB: number[]
): {
    center: [number, number];
    rotation: { angle: number; cos: number; sin: number };
    a: number;
    b: number;
} {
    // Calculate semi-major and semi-minor axes
    const a = Math.sqrt(Math.pow(vectorA[0] - center[0], 2) + Math.pow(vectorA[1] - center[1], 2));
    const b = Math.sqrt(Math.pow(vectorB[0] - center[0], 2) + Math.pow(vectorB[1] - center[1], 2));

    // Calculate rotation angle from center to vectorA
    const rotationAngle = Math.atan2(vectorA[1] - center[1], vectorA[0] - center[0]);
    const cos = Math.cos(rotationAngle);
    const sin = Math.sin(rotationAngle);

    return {
        center: [center[0], center[1]] as [number, number],
        rotation: { angle: rotationAngle, cos, sin },
        a,
        b,
    };
}

/**
 * Calculate focus points from center and vectors
 */
export function calcFocusPoints(
    center: number[],
    vectorA: number[],
    vectorB: number[]
): { f1: number[]; f2: number[] } {
    // Calculate semi-major and semi-minor axes
    const a = Math.sqrt(Math.pow(vectorA[0] - center[0], 2) + Math.pow(vectorA[1] - center[1], 2));
    const b = Math.sqrt(Math.pow(vectorB[0] - center[0], 2) + Math.pow(vectorB[1] - center[1], 2));

    // Calculate rotation angle from center to vectorA
    const rotationAngle = Math.atan2(vectorA[1] - center[1], vectorA[0] - center[0]);
    const cos = Math.cos(rotationAngle);
    const sin = Math.sin(rotationAngle);

    // Calculate focus points
    const c = Math.sqrt(Math.abs(a * a - b * b)); // Distance from center to focus
    const f1 = [center[0] + c * cos, center[1] + c * sin];
    const f2 = [center[0] - c * cos, center[1] - c * sin];

    return {
        f1,
        f2,
    };
}
