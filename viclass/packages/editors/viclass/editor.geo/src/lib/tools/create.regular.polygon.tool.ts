﻿import { point, Point, vector, Vector } from '@flatten-js/core';
import { MouseEventData, UIPointerEventData } from '@viclass/editor.core';
import { syncEndPreviewModeCommand } from '../cmd';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoElConstructionRequest, RegularPolygonToolState, RenderVector, RenderVertex } from '../model';
import { GeoEpsilon, GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pPolygon, PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import {
    nothing,
    nPoints,
    or,
    repeat,
    RepeatSelector,
    SelectedVertex,
    then,
    ThenSelector,
    vert,
    vertex,
} from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    assignNames,
    buildPreviewVertexRenderProp,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    remoteConstruct,
} from './util.tool';

/**
 * Clamp the number of edges of the regular polygon within [3, 8] and round to the nearest integer.
 */
function clampEdge(noEdge: number) {
    return Math.min(
        Math.max(Math.round(noEdge) || CreateRegularPolygonTool.EDGE_MIN, CreateRegularPolygonTool.EDGE_MIN),
        CreateRegularPolygonTool.EDGE_MAX
    );
}

/**
 * Tool for creating regular polygons. Allows users to draw a regular polygon by clicking on the canvas
 * and change the number of edges by scrolling the mouse wheel while holding Ctrl.
 */
export class CreateRegularPolygonTool extends GeometryTool<RegularPolygonToolState> {
    static EDGE_MIN = 3;
    static EDGE_MAX = 10;
    static POLYGON_PREVIEW_ID = -20;

    readonly toolType: GeometryToolType = 'CreateRegularPolygonTool';

    declare selLogic?: ThenSelector;
    private readonly pQ = new PreviewQueue();

    private curSelected?: SelectedVertex[];
    private curDoc?: GeoDocCtrl;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        // Register mouse wheel event to change the number of edges
        this.registerMouseHandling(
            {
                event: 'mousewheel',
                button: 0,
                pressedButton: 0,
                keys: ['ctrl'],
            },
            {
                event: 'wheel',
                button: 0,
                pressedButton: 0,
                keys: ['ctrl'],
            }
        );

        this.registerKeyboardHandling(
            {
                event: 'keyup',
                keys: ['x'],
                global: false,
            },
            {
                event: 'keyup',
                keys: ['X'],
                global: false,
            },
            {
                event: 'keyup',
                keys: ['shift'],
                global: false,
            }
        );

        this.createSelLogic();
    }

    private createSelLogic() {
        const first2Points = nPoints(this.pQ, this.pointerHandler.cursor, {
            count: 2,
            onComplete: this.first2Points.bind(this),
        });
        this.selLogic = then(
            [
                // first select two points, the first two points can be on an edge or existing points or free points
                first2Points,

                repeat<(RenderVertex | 'nothing')[]>( // allow the user to select existing points if the additional point match
                    or(
                        // [RenderVertex | 'nothing']
                        [
                            vertex({
                                genPreview: false,
                                cursor: this.pointerHandler.cursor,
                                cfunc: this.checkAdditionalPoints.bind(this),
                                refinedFilter: (el: RenderVertex) => {
                                    const first2Sel = [
                                        this.selLogic.selected[0],
                                        this.selLogic.selected[1],
                                    ] as SelectedVertex[];
                                    if (first2Sel.findIndex(sel => vert(sel).relIndex == el.relIndex) != -1)
                                        // filter out the first 2 selected point
                                        return false;
                                    const additionalSel = this.selLogic.get(
                                        'existingPoints'
                                    ) as RepeatSelector<RenderVertex>;
                                    if (additionalSel.selected && additionalSel.selected.includes(el)) return false; // filter out any points already selected additionally

                                    return true;
                                },
                            }),
                            nothing(),
                        ]
                    ),
                    { name: 'existingPoints', onPartialSelection: this.additionalPoints.bind(this), flatten: 1 }
                ),
            ],
            {
                flatten: 1, // this is important to unbox the first 2 points
                onComplete: this.performConstruction.bind(this),
            } // unbox the repeat  level so we have an array of SelectedVertex | RenderVertex type
        );
    }

    private first2Points(selector: RepeatSelector<SelectedVertex>, doc: GeoDocCtrl) {
        this.toolState.selectedExistingPoints = 0;
        this.refreshToolState();
    }

    private additionalPoints(newSel: (RenderVertex | 'nothing')[], curSel: RenderVertex[]) {
        if (newSel[0] == 'nothing') return false;
        else {
            // if additional point is accepted
            this.toolState.selectedExistingPoints++;
            this.refreshToolState();
            return true; // for now.
        }
    }

    private checkAdditionalPoints(v: RenderVertex, doc: GeoDocCtrl): boolean {
        if (!v) {
            return false;
        } else {
            // we get the list of all points for this draw mode, check if the vertex
            // is one of them, AND coincide with one of the
            const selected = this.selLogic.selected as SelectedVertex[];
            const { points } = this.pointsFromDrawMode(selected);

            const existingSel = this.selLogic.get('existingPoints') as RepeatSelector<RenderVertex>; // because of flattening, we can cast it to this type
            const erv = existingSel.selected;

            const pv = point(v.coords[0], v.coords[1]);
            if (erv && erv.length > 0) {
                // check if the selected vertex coincide with any of the existing vertex

                const coincideIdx = erv.findIndex(e => point(e.coords[0], e.coords[1]).distanceTo(pv)[0] <= GeoEpsilon);

                // because each vertex of the regular polygon is only selected once, so if one of the erv has been selected at one position, new point cannot be selected
                if (coincideIdx >= 0) return false;
            }

            // now check if the new point is coincide with one of the generated point, if yes, then we allow this point
            const gIdx = points.findIndex(p => {
                return p.distanceTo(pv)[0] <= GeoEpsilon;
            });

            if (gIdx >= 0) return true;

            return false;
        }
    }

    private async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const ts = this.toolState;
        const selected = selector.selected.filter(e => e != 'nothing') as SelectedVertex[];

        // sort the selection in the order of the vertices of the polygon
        const { points: gps } = this.pointsFromDrawMode(selected);
        const sorted: (SelectedVertex | RenderVertex)[] = [];
        const p0 = selected[0];
        const p1 = selected[1];

        let numSelectedVertice = 0; // the total number of selected vertice
        for (let g = 0; g < gps.length; g++) {
            // for each generated vertice, find the correspond selection
            let found = false;
            for (let i = 0; i < selected.length; i++) {
                const v = vert(selected[i]);
                const pi = point(v.coords[0], v.coords[1]);

                if (pi.distanceTo(gps[g])[0] < GeoEpsilon) {
                    sorted.push(selected[i]);
                    numSelectedVertice++;
                    found = true;
                }
            }
            if (!found) sorted.push(pVertex(-g - 30, [gps[g].x, gps[g].y, 0])); // if not found, create a render vertex at the position of the generated point
        }

        if (numSelectedVertice == 0) sorted.unshift(p1);
        if (numSelectedVertice <= 1) sorted.unshift(p0); // include the center point when assign name, because it is not in the sorted list

        const { pcs } = await assignNames(
            ctrl,
            sorted,
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Đa giác đều'
        );
        const pointsNeedConstructions = selected.map(s => vert(s).name); // only the points selected by the user will have a construction
        // filter the pcs outside of selected, because they are to be constructed from server side
        const constructPoints = pcs.filter(c => pointsNeedConstructions.includes(c.name));

        if (!pcs) {
            this.resetState();
            return;
        }

        let constructPolygon: GeoElConstructionRequest;
        if (numSelectedVertice <= 1) {
            // construct with center
            sorted.shift(); // remove the center point from list before joining for name
            if (numSelectedVertice == 0) sorted.shift(); // remove also the mid point of edge
            const polyName = sorted.map(s => vert(s).name).join('');
            if (ts.drawMode == 1)
                constructPolygon = this.buildPolygonFromCenterAndPointConstruction(
                    polyName,
                    vert(p0).name,
                    vert(p1).name
                );
            else if (ts.drawMode == 2)
                constructPolygon = this.buildPolygonFromCenterAndMidPointConstruction(
                    polyName,
                    vert(p0).name,
                    vert(p1).name
                );
        } else {
            const polyName = sorted.map(s => vert(s).name).join('');
            constructPolygon = this.buildPolygonFromChordConstruction(polyName);
        }

        try {
            await remoteConstruct(ctrl, constructPolygon, constructPoints, this.editor.geoGateway, 'Đa giác');
        } finally {
            setTimeout(() => this.resetState());
        }
    }

    /**
     * Return the list of positions of vertices and the two selected render vertex
     * @param selected The li
     */
    private pointsFromDrawMode(selected: SelectedVertex[]): { points: Point[]; v0: RenderVertex; v1: RenderVertex } {
        const ts = this.toolState;

        const v0 = vert(selected[0] as SelectedVertex);
        const v1 = vert(selected[1] as SelectedVertex);
        const p0 = point(v0.coords[0], v0.coords[1]);
        const px = point(v1.coords[0], v1.coords[1]);
        const { points } =
            ts.drawMode == 0
                ? regPolyVertFromChord(ts.noEdge, p0, px, ts.selecting)
                : ts.drawMode == 1
                  ? regPolyVertFromCenter(ts.noEdge, p0, px)
                  : regPolyVertFromCenterMid(ts.noEdge, p0, px);
        return {
            points,
            v0,
            v1,
        };
    }

    private doPreview(selected: SelectedVertex[], ctrl: GeoDocCtrl, pQ: PreviewQueue) {
        if (selected && selected.length >= 2) {
            // render the two selected point

            const { points, v0, v1 } = this.pointsFromDrawMode(selected);

            // Create the preview vertices and put on top of selected vertices if they are not previews
            // This give better controls and cleaner solution than highlight, since we just need to end the
            // preview to clear everything
            const v0Temp = v0.relIndex < 0 ? v0 : pVertex(-1000, v0.coords);
            pQ.add(v0Temp);
            const v1Temp = v1.relIndex < 0 ? v1 : pVertex(-1001, v1.coords);
            pQ.add(v1Temp);
            v0Temp.renderProp = buildPreviewVertexRenderProp();
            v1Temp.renderProp = buildPreviewVertexRenderProp();
            v0Temp.renderProp.pointColor = '#ff0000';
            v1Temp.renderProp.pointColor = '#0000ff';

            // we just draw the polygon without its vertices element, The individual element is synced by the vertex selector

            if (points.length >= 3) {
                pQ.add(
                    pPolygon(
                        ctrl,
                        CreateRegularPolygonTool.POLYGON_PREVIEW_ID,
                        points.map(p => [p.x, p.y, 0]),
                        true,
                        RenderVector
                    )
                );
            }
        }
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl) as SelectedVertex[];
        this.setCurrentPreview(selected, ctrl);
        if (this.selLogic.isAccepted) return;
        this.doPreview(selected as SelectedVertex[], ctrl, this.pQ);
        this.pQ.flush(ctrl);
    }

    /**
     * Reset the tool state to be ready to create a new polygon.
     * Clear the list of points, reset relIndex, nth, pointer down flag, click count.
     * Call resetState of the parent class.
     */
    override resetState() {
        this.selLogic.reset();
        this.curSelected = [];
        this.curDoc = undefined;
        super.resetState();
        // this.toolbar.update(this.toolType, new RegularPolygonToolState());
    }

    /**
     * Handle mouse events (mousewheel, wheel) to change the number of edges
     */
    override handleMouseEvent(event: MouseEventData<any>): MouseEventData<any> {
        switch (event.nativeEvent.type) {
            case 'mousewheel':
            case 'wheel': {
                this.onMouseWheel(event, this.curDoc);
                event.continue = false;

                break;
            }
            default:
                break;
        }
        return event;
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        let processed = false;
        const ts = this.toolState;
        if (event.eventType == 'keyup') {
            if (event.getKeys.includes('x')) {
                processed = true;
                this.rotateNextVert();
            }
            if (event.getKeys.includes('shift') && ts.selectedExistingPoints <= 0) {
                // when selected an existing point already, changing draw mode is not possible
                processed = true;
                ts.drawMode = ++ts.drawMode % 3;
            }

            if (processed) {
                this.refreshToolState();
                event.continue = false;
                event.nativeEvent.preventDefault();
            }
        }
        return event;
    }

    public refreshToolState() {
        this.toolbar.update(this.toolType, this.toolState);
        if (this.curSelected?.length > 0 && this.curDoc) {
            this.doPreview(this.curSelected, this.curDoc, this.pQ);
            this.pQ.flush(this.curDoc);
        }
    }

    public rotateNextVert() {
        const ts = this.toolState;
        if (ts.selectedExistingPoints > 0) return; // if already selected an existing point, rotate no longer possible
        ts.selecting = (ts.selecting + 1) % ts.noEdge;
        if (ts.selecting == 0) ts.selecting = 1;
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private setCurrentPreview(curSelected: SelectedVertex[], ctrl: GeoDocCtrl) {
        this.curSelected = curSelected;
        this.curDoc = ctrl;
    }

    /**
     * Handle mouse wheel event to increase/decrease the number of edges of the regular polygon.
     * Scroll up: increase edges, scroll down: decrease edges.
     */
    private async onMouseWheel(event: MouseEventData<any>, ctrl: GeoDocCtrl) {
        const ts = this.toolbar.toolState(this.toolType) as RegularPolygonToolState;
        if (ts.selectedExistingPoints > 0) return; // if already selected an existing point, changing number of edge is not possible.
        const wheel = (event.nativeEvent as WheelEvent).deltaY < 0 ? 1 : -1;
        ts.noEdge = clampEdge(ts.noEdge + wheel);

        if (ts.selecting >= ts.noEdge) ts.selecting = ts.selecting % ts.noEdge;
        if (ts.selecting == 0) ts.selecting = 1;

        syncEndPreviewModeCommand(ctrl);
        this.refreshToolState();
    }

    private buildPolygonFromCenterAndMidPointConstruction(
        polygonName: string,
        centerPointName: string,
        firstPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromCenterAndMidPoint'
        );
        construction.name = polygonName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-CenterPointOfPolygon',
                params: {
                    name: {
                        type: 'singleValue',
                        value: centerPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-MiddlePointIs',
                params: {
                    name: {
                        type: 'singleValue',
                        value: firstPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildPolygonFromCenterAndPointConstruction(
        polygonName: string,
        centerPointName: string,
        firstPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromCenterAndPoint'
        );
        construction.name = polygonName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-CenterPointOfPolygon',
                params: {
                    name: {
                        type: 'singleValue',
                        value: centerPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Point',
                params: {
                    name: {
                        type: 'singleValue',
                        value: firstPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildPolygonFromChordConstruction(polygonName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromChord'
        );
        construction.name = polygonName;
        construction.paramSpecs = [];

        return construction;
    }
}

function regPolyVertFromCenter(n: number, pc: Point, p0: Point): { points: Point[] } {
    if (n < 3) {
        throw new Error('Polygon must have at least 3 vertices.');
    }

    const angleStep = -(2 * Math.PI) / n; // Negative for clockwise

    const baseVector = vector(pc, p0);
    const vertices: Point[] = [];

    for (let i = 0; i < n; i++) {
        const rotated = baseVector.rotate(angleStep * i);
        const pt = pc.translate(rotated);
        pt.x = Number(pt.x.toFixed(10));
        pt.y = Number(pt.y.toFixed(10));
        vertices.push(pt);
    }

    return { points: vertices };
}

function regPolyVertFromCenterMid(n: number, pc: Point, pm: Point): { points: Point[] } {
    if (n < 3) {
        throw new Error('Polygon must have at least 3 vertices.');
    }

    const angleStep = -(2 * Math.PI) / n; // Clockwise

    // Vector from center to midpoint of an edge
    const midVector = vector(pc, pm);
    const halfInteriorAngle = Math.PI / n;

    // Compute full radius (from center to vertex)
    const radius = midVector.length / Math.cos(halfInteriorAngle);

    // Rotate midVector by -π/n to get the direction to first vertex (clockwise)
    const toVertexVector = midVector.normalize().multiply(radius).rotate(-halfInteriorAngle);

    // First vertex
    const p0 = pc.translate(toVertexVector);

    // Generate all vertices in clockwise order
    const vertices: Point[] = [];
    for (let i = 0; i < n; i++) {
        const angle = angleStep * i;
        const rotated = toVertexVector.rotate(angle);
        const pt = pc.translate(rotated);
        pt.x = Number(pt.x.toFixed(10));
        pt.y = Number(pt.y.toFixed(10));
        vertices.push(pt);
    }

    return { points: vertices };
}

/**
 * Generates vertices of a regular polygon with:
 * - `p0` at index 0
 * - `px` at index `xth`
 * - Total of `n` vertices in either 'cw' or 'ccw' direction
 */
function regPolyVertFromChord(
    n: number,
    p0: Point,
    px: Point,
    xth: number
): { points: Point[]; center: Point; perp: Vector } {
    if (p0.distanceTo(px)[0] < GeoEpsilon) return { points: [], center: p0, perp: vector(0, 0) }; // don't generate if too small

    if (n < 3 || xth < 1 || xth >= n) {
        throw new Error('Invalid input: n must be ≥ 3 and 1 ≤ xth < n');
    }

    const angleStep = (2 * Math.PI) / n;
    const signedAngleStep = -angleStep;
    const angleBetween = signedAngleStep * xth;

    const chordVector = vector(p0, px);
    const chordLength = chordVector.length;
    const angleHalf = angleBetween / 2;

    const dir = Math.abs(angleHalf) > Math.PI / 2 ? -1 : 1;
    const radius = chordLength / (2 * Math.sin(Math.abs(angleHalf)));

    // Midpoint of chord
    const mid = p0.translate(chordVector.multiply(0.5));

    // Perpendicular vector to find center
    const perp = chordVector.rotate(-Math.PI / 2).multiply(dir); // when the angle between p0 and px > 180, the center of the polygon flip
    const distToCenter = Math.sqrt(radius * radius - (chordLength / 2) ** 2);
    const center = mid.translate(perp.normalize().multiply(distToCenter));

    // Vector from center to p0
    const baseVector = vector(center, p0);

    // Generate vertices
    const vertices: Point[] = [p0];
    for (let i = 1; i < n; i++) {
        const angle = signedAngleStep * i;
        const rotated = baseVector.rotate(angle);
        const pt = center.translate(rotated);
        pt.x = Number(pt.x.toFixed(10));
        pt.y = Number(pt.y.toFixed(10));
        vertices.push(pt);
    }

    // // Reorder so that p0 is at index 0
    // const indexOfP0 = vertices.findIndex(pt => pt.equalTo(p0));
    // const reordered: Point[] = [];
    // for (let i = 0; i < n; i++) {
    //     const idx = (indexOfP0 + i) % n;
    //     reordered.push(vertices[idx]);
    // }

    // Ensure px is at index xth
    // if (!reordered[xth].equalTo(px)) {
    //     throw new Error('Could not place px at xth index. Check for precision issues.');
    // }

    return { points: vertices, center, perp };
}
