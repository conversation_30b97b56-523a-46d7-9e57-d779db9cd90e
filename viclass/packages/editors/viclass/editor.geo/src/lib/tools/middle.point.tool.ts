import { UIPointerEventData } from '@viclass/editor.core';
import { syncRemovePreviewCmd } from '../cmd';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    RenderLineSegment,
    RenderSector,
    RenderVector,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { or, OrSelector, repeat, vertex, vertexOnStroke } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    buildPreviewVertexRenderProp,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    pickPointName,
    projectOnEl,
    remoteConstruct,
    requestElementNames,
} from './util.tool';

type SelectedInput = [RenderVertex, RenderVertex] | [RenderLineSegment | RenderVector | RenderSector];
export class MiddlePointTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'MiddlePointTool';
    declare protected selLogic?: OrSelector<SelectedInput>;

    pQ: PreviewQueue = new PreviewQueue();
    points: RenderVertex[] = [];

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    createSelLogic() {
        this.selLogic = or<[RenderVertex, RenderVertex] | [RenderLineSegment]>(
            // either
            [
                repeat<RenderVertex>( // select two point repeatedly
                    vertex({
                        genPreview: false,
                        previewQueue: this.pQ,
                        cursor: this.pointerHandler.cursor,
                        refinedFilter: this.excludeSelection.bind(this), // do not reselect anything has been selected
                        onComplete: selector => this.points.push(selector.selected),
                    }),
                    {
                        count: 2,
                    }
                ),
                vertexOnStroke({
                    // or select a line segment
                    selectableStrokeTypes: ['RenderLineSegment', 'RenderVector', 'RenderSector'],
                    cursor: this.pointerHandler.cursor,
                    tfunc: this.pointOnObjectTransform.bind(this),
                    syncPreview: true,
                    genPreview: true,
                }),
            ],
            { flatten: true, onComplete: this.performConstruction.bind(this) }
        );
    }

    override resetState() {
        super.resetState();
        this.selLogic?.reset();
        this.points = [];
    }

    excludeSelection(el: RenderVertex) {
        return !this.points.includes(el);
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    async performConstruction(selector: OrSelector<SelectedInput>, doc: GeoDocCtrl) {
        const selected = selector.selected;
        let sCoords: number[];
        let eCoords: number[];
        let pV: RenderVertex;
        let lineName: string = undefined;
        const sector: string = undefined;
        if (selected[0].type === 'RenderVertex') {
            // Two points are selected
            sCoords = selected[0].coords;
            eCoords = selected[1].coords;
            pV = pVertex(-20, [(sCoords[0] + eCoords[0]) / 2, (sCoords[1] + eCoords[1]) / 2, 0]);
            lineName = `${selected[0].name}${selected[1].name}`;
        } else {
            pV = selected[1];
            if (selected[0].type == 'RenderLineSegment' || selected[0].type == 'RenderVector') {
                lineName = selected[0].name;
            } else {
                throw new Error('SECTOR SUPPORT NOT IMPLEMENTED');
            }
        }

        const middlePointName = (
            await requestElementNames(doc, this.toolbar.getTool('NamingElementTool') as NamingElementTool, [
                {
                    objName: 'Trung Điểm',
                    originElement: [pV],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0]?.[0];
        if (!middlePointName) return;

        try {
            if (lineName) {
                const constructionFromLine = this.buildPointConstructionFromLine(middlePointName, lineName);

                await remoteConstruct(doc, constructionFromLine, [], this.editor.geoGateway, 'Trung điểm');
            } else {
                // TODO : construct from sector
            }
        } finally {
            this.resetState();
        }
    }

    /**
     * Middle point of the object is the selection of vertex on the object with snapping to the center
     * @param el
     * @param preview
     * @param ctrl
     */
    pointOnObjectTransform(
        el: RenderLineSegment | RenderVector | RenderSector,
        preview: RenderVertex,
        ctrl: GeoDocCtrl
    ): RenderVertex {
        const start = el.coord('start', ctrl.rendererCtrl);
        const end = el.coord('end', ctrl.rendererCtrl);

        if (start && end) {
            const middle = projectOnEl(
                el,
                [start[0] + (end[0] - start[0]) / 2, start[1] + (end[1] - start[1]) / 2, 0],
                ctrl.rendererCtrl
            );

            preview.coords = middle;
            preview.renderProp = buildPreviewVertexRenderProp();
            preview.renderProp.pointColor = '#ff0000';
        }

        return preview;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (selected) {
            if (selected.length == 2 && selected[0].type == 'RenderVertex') {
                // has all data, preview triangle
                const p1 = selected[0] as RenderVertex;
                const p2 = selected[1] as RenderVertex;
                const v = pVertex(-20, [(p1.coords[0] + p2.coords[0]) / 2, (p1.coords[1] + p2.coords[1]) / 2, 0]);
                v.renderProp = buildPreviewVertexRenderProp();
                v.renderProp.pointColor = '#ff0000';
                this.pQ.add(v);
                this.pQ.add(pLine(ctrl, -21, RenderLineSegment, p1.coords, p2.coords));
            }
        } else {
            if (ctrl.rendererCtrl.previewElAt(-20)) {
                syncRemovePreviewCmd(-20, ctrl);
                syncRemovePreviewCmd(-21, ctrl);
            }
        }
        this.pQ.flush(ctrl);
    }

    private buildPointConstructionFromLine(name: string, lineName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Point/MiddlePointEC', 'Point', 'LineSegment');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-MiddlePointOfLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
        ];

        return construction;
    }
}
