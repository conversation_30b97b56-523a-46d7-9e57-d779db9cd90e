import { Circle, Line, Point, point, Ray, Segment } from '@flatten-js/core';
import { RenderCircle, RenderEllipse, RenderLine, RenderSector } from '../model';
import { GeoDocCtrl } from '../objects';
import { createFlattenCircle, createFlattenLine } from './util.flatten';
import { pointsByRotation, pointsOnEllipse, pointsOnParallelVector } from './util.order';

// =================== TYPES & UTILITIES ===================

function safeIntersection(operation: () => Point[], errorContext: string) {
    try {
        const points = operation();
        return points;
    } catch (error) {
        console.warn(`Error in ${errorContext}:`, error);
        return [];
    }
}

function createSectorCircle(sector: RenderSector, docCtrl: GeoDocCtrl): Circle {
    const centerCoords = sector.coord('center', docCtrl.rendererCtrl);
    const center = point(centerCoords[0], centerCoords[1]);
    return new Circle(center, sector.radius);
}

function getEllipseGeometry(renderEllipse: RenderEllipse, docCtrl: GeoDocCtrl) {
    // Sử dụng coord method để lấy center (đã handle cả focus points và center-vectors mode)
    const centerCoords = renderEllipse.coord('center', docCtrl.rendererCtrl);
    const center = point(centerCoords[0], centerCoords[1]);

    // Tính rotation từ geometry thực tế
    let rotation = 0;
    try {
        // Thử lấy f1 để tính rotation (focus points mode)
        const f1Coords = renderEllipse.coord('f1', docCtrl.rendererCtrl);
        rotation = Math.atan2(f1Coords[1] - centerCoords[1], f1Coords[0] - centerCoords[0]);
    } catch {
        try {
            // Nếu không có f1, thử lấy va (center-vectors mode)
            const vaCoords = renderEllipse.coord('va', docCtrl.rendererCtrl);
            rotation = Math.atan2(vaCoords[1] - centerCoords[1], vaCoords[0] - centerCoords[0]);
        } catch {
            // Fallback to stored rotation
            rotation = renderEllipse.rotate || 0;
        }
    }

    return {
        center,
        a: renderEllipse.a,
        b: renderEllipse.b,
        rotation,
    };
}

function isPointOnVector(pt: Point, end: number[]): boolean {
    return Math.abs(pt.x - end[0]) < Number.EPSILON && Math.abs(pt.y - end[1]) < Number.EPSILON;
}

function isPointInSector(pt: Point, sector: RenderSector, docCtrl: GeoDocCtrl): boolean {
    try {
        const centerCoords = sector.coord('center', docCtrl.rendererCtrl);
        const center = point(centerCoords[0], centerCoords[1]);

        const dx = pt.x - center.x;
        const dy = pt.y - center.y;
        let pointAngle = Math.atan2(dy, dx);
        if (pointAngle < 0) pointAngle += 2 * Math.PI;

        const startCoords = sector.coord('start', docCtrl.rendererCtrl);
        const endCoords = sector.coord('end', docCtrl.rendererCtrl);
        if (!startCoords || !endCoords) return true;

        const startDx = startCoords[0] - center.x;
        const startDy = startCoords[1] - center.y;
        let startAngle = Math.atan2(startDy, startDx);
        if (startAngle < 0) startAngle += 2 * Math.PI;

        const endDx = endCoords[0] - center.x;
        const endDy = endCoords[1] - center.y;
        let endAngle = Math.atan2(endDy, endDx);
        if (endAngle < 0) endAngle += 2 * Math.PI;

        return startAngle <= endAngle
            ? pointAngle >= startAngle && pointAngle <= endAngle
            : pointAngle >= startAngle || pointAngle <= endAngle;
    } catch {
        return false;
    }
}

// =================== INTERSECTION FUNCTIONS ===================

export function calculateLineLineIntersection(line1: RenderLine, line2: RenderLine, docCtrl: GeoDocCtrl) {
    return safeIntersection(() => {
        const flattenLine1 = createFlattenLine(line1, docCtrl);
        const flattenLine2 = createFlattenLine(line2, docCtrl);
        return flattenLine1.intersect(flattenLine2);
    }, 'calculateLineLineIntersection');
}

export function calculateLineCircleIntersection(line: RenderLine, circle: RenderCircle, docCtrl: GeoDocCtrl) {
    return safeIntersection(() => {
        const flattenLine = createFlattenLine(line, docCtrl);
        const flattenCircle = createFlattenCircle(circle, docCtrl);
        const intersections = flattenLine.intersect(flattenCircle);

        if (intersections.length <= 1) return intersections;

        const orderedVector = line.orderedVector(docCtrl.rendererCtrl);
        const directionVector = point(orderedVector[0], orderedVector[1]);
        return pointsOnParallelVector(directionVector, intersections);
    }, 'calculateLineCircleIntersection');
}

export function calculateCircleCircleIntersection(circle1: RenderCircle, circle2: RenderCircle, docCtrl: GeoDocCtrl) {
    return safeIntersection(() => {
        const flattenCircle1 = createFlattenCircle(circle1, docCtrl);
        const flattenCircle2 = createFlattenCircle(circle2, docCtrl);
        const intersections = flattenCircle1.intersect(flattenCircle2);

        if (intersections.length <= 1) return intersections;

        const center1 = flattenCircle1.center;
        const center2 = flattenCircle2.center;
        const connectionVector = point(center2.x - center1.x, center2.y - center1.y);
        return pointsByRotation(connectionVector, center1, intersections);
    }, 'calculateCircleCircleIntersection');
}

export function calculateLineSectorIntersection(line: RenderLine, sector: RenderSector, docCtrl: GeoDocCtrl) {
    return safeIntersection(() => {
        const flattenLine = createFlattenLine(line, docCtrl);
        const sectorCircle = createSectorCircle(sector, docCtrl);

        const intersections = flattenLine.intersect(sectorCircle).filter(pt => isPointInSector(pt, sector, docCtrl));

        if (line.type === 'RenderVector') {
            const end = line.coord('end', docCtrl.rendererCtrl);
            return intersections.filter(pt => isPointOnVector(pt, end));
        }

        if (intersections.length <= 1) return intersections;

        const orderedVector = line.orderedVector(docCtrl.rendererCtrl);
        const directionVector = point(orderedVector[0], orderedVector[1]);
        return pointsOnParallelVector(directionVector, intersections);
    }, 'calculateLineSectorIntersection');
}

export function calculateSectorSectorIntersection(sector1: RenderSector, sector2: RenderSector, docCtrl: GeoDocCtrl) {
    return safeIntersection(() => {
        const circle1 = createSectorCircle(sector1, docCtrl);
        const circle2 = createSectorCircle(sector2, docCtrl);

        const intersections = circle1
            .intersect(circle2)
            .filter(pt => isPointInSector(pt, sector1, docCtrl) && isPointInSector(pt, sector2, docCtrl));

        if (intersections.length <= 1) return intersections;

        const center1 = circle1.center;
        const center2 = circle2.center;
        const connectionVector = point(center2.x - center1.x, center2.y - center1.y);
        return pointsByRotation(connectionVector, center1, intersections);
    }, 'calculateSectorSectorIntersection');
}

export function calculateCircleSectorIntersection(circle: RenderCircle, sector: RenderSector, docCtrl: GeoDocCtrl) {
    return safeIntersection(() => {
        const flattenCircle = createFlattenCircle(circle, docCtrl);
        const sectorCircle = createSectorCircle(sector, docCtrl);

        const intersections = flattenCircle.intersect(sectorCircle).filter(pt => isPointInSector(pt, sector, docCtrl));

        if (intersections.length <= 1) return intersections;

        const circleCenter = flattenCircle.center;
        const sectorCenter = sectorCircle.center;
        const connectionVector = point(sectorCenter.x - circleCenter.x, sectorCenter.y - circleCenter.y);
        return pointsByRotation(connectionVector, circleCenter, intersections);
    }, 'calculateCircleSectorIntersection');
}

export function calculateLineEllipseIntersection(line: RenderLine, ellipse: RenderEllipse, docCtrl: GeoDocCtrl) {
    return safeIntersection(() => {
        const flattenLine = createFlattenLine(line, docCtrl);
        const ellipseGeometry = getEllipseGeometry(ellipse, docCtrl);

        // Validate ellipse geometry
        if (!ellipseGeometry.center || ellipseGeometry.a <= 0 || ellipseGeometry.b <= 0) {
            console.warn('Invalid ellipse geometry:', ellipseGeometry);
            return [];
        }

        const intersections = calculateLineEllipseIntersectionAnalytical(flattenLine, ellipseGeometry);

        if (intersections.length <= 1) return intersections;

        const orderedVector = line.orderedVector(docCtrl.rendererCtrl);
        const directionVector = point(orderedVector[0], orderedVector[1]);
        return pointsOnParallelVector(directionVector, intersections);
    }, 'calculateLineEllipseIntersection');
}

export function calculateCircleEllipseIntersection(circle: RenderCircle, ellipse: RenderEllipse, docCtrl: GeoDocCtrl) {
    return safeIntersection(() => {
        const flattenCircle = createFlattenCircle(circle, docCtrl);
        const ellipseGeometry = getEllipseGeometry(ellipse, docCtrl);
        const intersections = calculateCircleEllipseIntersectionNumerical(flattenCircle, ellipseGeometry);

        if (intersections.length <= 1) return intersections;

        const circleCenter = flattenCircle.center;
        const ellipseCenter = ellipseGeometry.center;
        const connectionVector = point(circleCenter.x - ellipseCenter.x, circleCenter.y - ellipseCenter.y);
        return pointsByRotation(connectionVector, ellipseCenter, intersections);
    }, 'calculateCircleEllipseIntersection');
}

export function calculateEllipseEllipseIntersection(
    ellipse1: RenderEllipse,
    ellipse2: RenderEllipse,
    docCtrl: GeoDocCtrl
) {
    return safeIntersection(() => {
        const ellipse1Geometry = getEllipseGeometry(ellipse1, docCtrl);
        const ellipse2Geometry = getEllipseGeometry(ellipse2, docCtrl);
        const intersections = calculateEllipseEllipseIntersectionNumerical(ellipse1Geometry, ellipse2Geometry);

        if (intersections.length <= 1) return intersections;

        const refEllipse = ellipse1Geometry.a >= ellipse2Geometry.a ? ellipse1 : ellipse2;
        const refEllipseGeometry = ellipse1Geometry.a >= ellipse2Geometry.a ? ellipse1Geometry : ellipse2Geometry;

        const f2Coords = refEllipse.coord('f2', docCtrl.rendererCtrl);
        const center = refEllipseGeometry.center;
        const f2 = point(f2Coords[0], f2Coords[1]);

        return pointsOnEllipse({ center, f2 }, intersections);
    }, 'calculateEllipseEllipseIntersection');
}

export function calculateEllipseSectorIntersection(ellipse: RenderEllipse, sector: RenderSector, docCtrl: GeoDocCtrl) {
    return safeIntersection(() => {
        const ellipseGeometry = getEllipseGeometry(ellipse, docCtrl);
        const sectorCircle = createSectorCircle(sector, docCtrl);

        const intersections = calculateCircleEllipseIntersectionNumerical(sectorCircle, ellipseGeometry).filter(pt =>
            isPointInSector(pt, sector, docCtrl)
        );

        if (intersections.length <= 1) return intersections;

        const ellipseCenter = ellipseGeometry.center;
        const sectorCenter = sectorCircle.center;
        const connectionVector = point(sectorCenter.x - ellipseCenter.x, sectorCenter.y - ellipseCenter.y);
        return pointsByRotation(connectionVector, ellipseCenter, intersections);
    }, 'calculateEllipseSectorIntersection');
}

// =================== NUMERICAL CALCULATION HELPERS ===================

function calculateLineEllipseIntersectionAnalytical(
    line: Line | Ray | Segment,
    ellipse: { center: Point; a: number; b: number; rotation: number }
): Point[] {
    try {
        // Get line parameters
        let lineStart: Point;
        let lineDirection: Point;
        let isSegment = false;
        let segmentLength = 0;

        if (line instanceof Line) {
            // For infinite line, direction vector is normal rotated 90° clockwise
            // If normal is (nx, ny), rotate90CW gives (ny, -nx)
            lineDirection = point(line.norm.y, -line.norm.x);
            lineStart = line.pt;
        } else if (line instanceof Ray) {
            lineStart = line.start;
            // Ray norm is already the direction vector
            lineDirection = point(line.norm.y, -line.norm.x);
        } else if (line instanceof Segment) {
            lineStart = line.start;
            const end = line.end;
            const segmentVector = point(end.x - lineStart.x, end.y - lineStart.y);
            segmentLength = Math.sqrt(segmentVector.x * segmentVector.x + segmentVector.y * segmentVector.y);

            if (segmentLength === 0) return []; // Degenerate segment

            // Keep original direction vector (not normalized)
            lineDirection = segmentVector;
            isSegment = true;
        } else {
            return [];
        }

        // Normalize direction vector if not segment
        if (!isSegment) {
            const dirLength = Math.sqrt(lineDirection.x * lineDirection.x + lineDirection.y * lineDirection.y);
            if (dirLength === 0) return [];
            lineDirection = point(lineDirection.x / dirLength, lineDirection.y / dirLength);
        }

        // Translate coordinate system so ellipse center is at origin
        const translatedStart = point(lineStart.x - ellipse.center.x, lineStart.y - ellipse.center.y);

        // Rotate coordinate system to align with ellipse axes
        const cos_theta = Math.cos(-ellipse.rotation);
        const sin_theta = Math.sin(-ellipse.rotation);

        // Rotate line start point
        const x0 = translatedStart.x * cos_theta - translatedStart.y * sin_theta;
        const y0 = translatedStart.x * sin_theta + translatedStart.y * cos_theta;

        // Rotate line direction
        const dx = lineDirection.x * cos_theta - lineDirection.y * sin_theta;
        const dy = lineDirection.x * sin_theta + lineDirection.y * cos_theta;

        // Solve quadratic equation for line-ellipse intersection
        // Line: x = x0 + t*dx, y = y0 + t*dy
        // Ellipse: x²/a² + y²/b² = 1
        // Substituting: (x0 + t*dx)²/a² + (y0 + t*dy)²/b² = 1

        const a2 = ellipse.a * ellipse.a;
        const b2 = ellipse.b * ellipse.b;

        const A = (dx * dx) / a2 + (dy * dy) / b2;
        const B = 2 * ((x0 * dx) / a2 + (y0 * dy) / b2);
        const C = (x0 * x0) / a2 + (y0 * y0) / b2 - 1;

        // Handle degenerate case where A = 0 (line parallel to ellipse axis)
        if (Math.abs(A) < 1e-10) {
            if (Math.abs(B) < 1e-10) {
                // Line is contained in ellipse or doesn't intersect
                return Math.abs(C) < 1e-10 ? [] : [];
            }
            // Linear equation: Bt + C = 0
            const t = -C / B;
            if (isFinite(t)) {
                // Validate t for different line types
                if (line instanceof Ray && t < 0) return [];
                if (isSegment && (t < 0 || t > 1)) return [];

                const x_rot = x0 + t * dx;
                const y_rot = y0 + t * dy;

                // Check if point is on ellipse
                if (Math.abs((x_rot * x_rot) / a2 + (y_rot * y_rot) / b2 - 1) < 1e-8) {
                    // Rotate back to original orientation (inverse rotation)
                    const x_final = x_rot * cos_theta + y_rot * sin_theta;
                    const y_final = -x_rot * sin_theta + y_rot * cos_theta;
                    const finalPoint = point(x_final + ellipse.center.x, y_final + ellipse.center.y);
                    return [finalPoint];
                }
            }
            return [];
        }

        const discriminant = B * B - 4 * A * C;

        if (discriminant < 0) {
            return []; // No intersection
        }

        const results: Point[] = [];
        const sqrtDiscriminant = Math.sqrt(discriminant);

        // Calculate t values
        const t1 = (-B - sqrtDiscriminant) / (2 * A);
        const t2 = (-B + sqrtDiscriminant) / (2 * A);

        // Convert back to original coordinate system
        for (const t of [t1, t2]) {
            if (!isFinite(t)) continue;

            // Check if point is valid for the specific line type
            if (line instanceof Ray && t < 0) continue;
            if (isSegment && (t < 0 || t > 1)) continue;

            // Calculate point in rotated coordinate system
            const x_rot = x0 + t * dx;
            const y_rot = y0 + t * dy;

            // Rotate back to original orientation (inverse rotation)
            const x_final = x_rot * cos_theta + y_rot * sin_theta;
            const y_final = -x_rot * sin_theta + y_rot * cos_theta;

            // Translate back to original position
            const finalPoint = point(x_final + ellipse.center.x, y_final + ellipse.center.y);

            results.push(finalPoint);
        }

        return removeDuplicatePoints(results, 1e-8);
    } catch (error) {
        console.warn('Error in calculateLineEllipseIntersectionAnalytical:', error);
        return [];
    }
}

function calculateCircleEllipseIntersectionNumerical(
    circle: Circle,
    ellipse: { center: Point; a: number; b: number; rotation: number }
): Point[] {
    const results: Point[] = [];
    const numSamples = 720;

    for (let i = 0; i < numSamples; i++) {
        const t = (2 * Math.PI * i) / numSamples;
        const ellipsePoint = getEllipsePoint(ellipse, t);

        // Check if point is on the circle
        const distToCenter = Math.sqrt(
            (ellipsePoint.x - circle.center.x) ** 2 + (ellipsePoint.y - circle.center.y) ** 2
        );

        if (Math.abs(distToCenter - circle.r) < Number.EPSILON) {
            results.push(ellipsePoint);
        }
    }

    return removeDuplicatePoints(results, Number.EPSILON);
}

function calculateEllipseEllipseIntersectionNumerical(
    ellipse1: { center: Point; a: number; b: number; rotation: number },
    ellipse2: { center: Point; a: number; b: number; rotation: number }
): Point[] {
    const results: Point[] = [];
    const numSamples = 1800;

    for (let i = 0; i < numSamples; i++) {
        const t = (2 * Math.PI * i) / numSamples;
        const ellipse1Point = getEllipsePoint(ellipse1, t);

        if (isPointOnEllipse(ellipse1Point, ellipse2, Number.EPSILON)) {
            results.push(ellipse1Point);
        }
    }

    return removeDuplicatePoints(results, Number.EPSILON);
}

function getEllipsePoint(ellipse: { center: Point; a: number; b: number; rotation: number }, t: number): Point {
    const x = ellipse.a * Math.cos(t);
    const y = ellipse.b * Math.sin(t);

    const cos_rot = Math.cos(ellipse.rotation);
    const sin_rot = Math.sin(ellipse.rotation);
    const x_rot = x * cos_rot - y * sin_rot;
    const y_rot = x * sin_rot + y * cos_rot;

    return point(ellipse.center.x + x_rot, ellipse.center.y + y_rot);
}

function isPointOnEllipse(
    pt: Point,
    ellipse: { center: Point; a: number; b: number; rotation: number },
    tolerance: number = Number.EPSILON
): boolean {
    const dx = pt.x - ellipse.center.x;
    const dy = pt.y - ellipse.center.y;

    const cos_rot = Math.cos(-ellipse.rotation);
    const sin_rot = Math.sin(-ellipse.rotation);
    const x_rot = dx * cos_rot - dy * sin_rot;
    const y_rot = dx * sin_rot + dy * cos_rot;

    const ellipseValue = (x_rot * x_rot) / (ellipse.a * ellipse.a) + (y_rot * y_rot) / (ellipse.b * ellipse.b);
    return Math.abs(ellipseValue - 1) <= tolerance;
}

function removeDuplicatePoints(points: Point[], tolerance: number): Point[] {
    const result: Point[] = [];

    for (const pt of points) {
        let isDuplicate = false;
        for (const existing of result) {
            const distance = Math.sqrt((pt.x - existing.x) ** 2 + (pt.y - existing.y) ** 2);
            if (distance < tolerance) {
                isDuplicate = true;
                break;
            }
        }
        if (!isDuplicate) {
            result.push(pt);
        }
    }

    return result;
}
