import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderCircleShape,
    RenderEllipseShape,
    RenderLine,
    RenderPolygon,
    RenderSectorShape,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoObjectType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { calculateSymmetricPointsThroughLine, createSymmetry } from './symmetry.tool.utils';
import {
    addHistoryItemFromConstructionResponse,
    buildPreviewVertexRenderProp,
    handleIfPointerNotInError,
    isElementLine,
    pickPointName,
    requestElementNames,
} from './util.tool';

/**
 *
 * <AUTHOR>
 */
export class CreateSymmetricThroughLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateSymmetricThroughLineTool';

    private lineStart: RenderLine;
    private symmetricShape: GeoRenderElement;
    private removePreviewFunc: Function = null;
    private isPointerDown = false;
    private selectionStage = 0; // 0: nothing selected, 1: first element selected

    protected override readonly filterElementFunc = (el: GeoRenderElement) =>
        ((isElementLine(el) && !this.lineStart) || !this.symmetricShape) && el.type != 'RenderAngle';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // pointerdown events
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', keys: ['shift'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // pointerup events
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', keys: ['shift'], button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            // pointermove events
            { event: 'pointermove', button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointermove', button: 0, keys: ['shift'], pointerTypes: pointerTypeMouse },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.lineStart = undefined;
        this.symmetricShape = undefined;
        this.isPointerDown = false;
        this.selectionStage = 0;
        if (this.removePreviewFunc) {
            this.removePreviewFunc();
            this.removePreviewFunc = null;
        }
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;

        const { ctrl, hitCtx, hitEl } = this.posAndCtrl(event, false);

        if (hitEl) {
            ctrl.editor.selectElement(hitCtx, true);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        if (this.selectionStage === 0) {
            await this.onFirstElementSelection(event);
        } else if (this.selectionStage === 1) {
            await this.onFinalElementSelection(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onFirstElementSelection(event: GeoPointerEvent) {
        const { ctrl, hitCtx, hitEl } = this.posAndCtrl(event, false);

        if (hitEl) {
            if (isElementLine(hitEl) && !this.lineStart) {
                this.lineStart = hitEl as RenderLine;
                this.selectionStage = 1;
            } else if (!this.symmetricShape) {
                this.symmetricShape = hitEl;
                this.selectionStage = 1;
            }
            ctrl.editor.selectElement(hitCtx, true);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onFinalElementSelection(event: GeoPointerEvent) {
        const { ctrl, hitCtx, hitEl } = this.posAndCtrl(event, false);

        if (hitEl) {
            if (isElementLine(hitEl) && !this.lineStart) {
                this.lineStart = hitEl as RenderLine;
            } else if (!this.symmetricShape) {
                this.symmetricShape = hitEl;
            }

            ctrl.editor.selectElement(hitCtx, true);

            if (this.lineStart && this.symmetricShape) {
                // We have both elements, proceed with creating symmetry
                await this.createSymmetricObject(ctrl);
            }
        }
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (!this.lineStart && !this.symmetricShape) return;
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        const { ctrl, hitEl, renderer } = this.posAndCtrl(event);

        // Clear previous preview
        if (this.removePreviewFunc != null) {
            this.removePreviewFunc();
            this.removePreviewFunc = null;
        }

        if (hitEl) {
            // Show preview based on what we have and what's under the pointer
            if (this.lineStart && hitEl !== this.lineStart) {
                // We have a symmetry line, show preview for symmetric of current element
                const lineVector = this.lineStart.vector;
                const startPoint = renderer.elementAt(this.lineStart.startPointIdx) as RenderVertex;

                this.removePreviewFunc = createSymmetry(
                    hitEl,
                    ctrl,
                    renderer,
                    calculateSymmetricPointsThroughLine(startPoint.coords, lineVector)
                );
            } else if (this.symmetricShape && isElementLine(hitEl) && hitEl !== this.symmetricShape) {
                // We have a shape, show preview of its symmetric through current line
                const lineStart = hitEl as RenderLine;
                const lineVector = lineStart.vector;
                const startPoint = renderer.elementAt(lineStart.startPointIdx) as RenderVertex;

                this.removePreviewFunc = createSymmetry(
                    this.symmetricShape,
                    ctrl,
                    renderer,
                    calculateSymmetricPointsThroughLine(startPoint.coords, lineVector),
                    ['RenderSectorShape']
                );
            }
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async createSymmetricObject(ctrl: GeoDocCtrl) {
        let construct: GeoElConstructionRequest;
        const sS = this.symmetricShape;
        const lS = this.lineStart; // Symmetry line

        const getSymmetricPointCoords = (coords: number[][]) =>
            calculateSymmetricPointsThroughLine(
                (ctrl.rendererCtrl.elementAt(lS.startPointIdx) as RenderVertex).coords,
                lS.vector
            )(coords);

        const tempVertexProps = {
            name: undefined,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            usable: true,
            valid: true,
        } as const;

        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;

        switch (sS.type) {
            case 'RenderLine':
            case 'RenderLineSegment':
            case 'RenderRay':
            case 'RenderVector': {
                const lineElement = sS as RenderLine;

                const [origP1, origP2] = [
                    ctrl.rendererCtrl.elementAt(lineElement.startPointIdx) as RenderVertex,
                    lineElement.endPointIdx !== null
                        ? (ctrl.rendererCtrl.elementAt(lineElement.endPointIdx) as RenderVertex)
                        : null,
                ];

                const symmetricCoords = getSymmetricPointCoords(
                    origP2 ? [origP1.coords, origP2.coords] : [origP1.coords]
                );
                const [symP1Coords, symP2Coords] = symmetricCoords;

                const tempSymmetricPoints = [
                    { ...tempVertexProps, coords: symP1Coords, relIndex: -1 },
                    symP2Coords ? { ...tempVertexProps, coords: symP2Coords, relIndex: -2 } : null,
                ].filter(Boolean) as RenderVertex[];

                const newPointNames = await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Điểm',
                        originElement: tempSymmetricPoints,
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ]);

                if (newPointNames.length < tempSymmetricPoints.length || newPointNames.some(name => !name)) {
                    this.resetState();
                    return;
                }

                construct = this.buildLineConstruction(sS.name, sS.elType, lS.name, lS.elType);
                construct.name = newPointNames.join('');
                break;
            }
            case 'RenderVertex': {
                const originalPoint = sS as RenderVertex;
                const [newSymmetricPointCoords] = getSymmetricPointCoords([originalPoint.coords]);
                const tempSymmetricVertex: RenderVertex = {
                    ...tempVertexProps,
                    coords: newSymmetricPointCoords,
                    relIndex: -1,
                };
                const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
                const newNameArr = (
                    await requestElementNames(ctrl, nt, [
                        {
                            objName: 'Điểm',
                            originElement: [tempSymmetricVertex],
                            pickName: pickPointName,
                            namesToAvoid: [],
                        },
                    ])
                )[0];
                if (!newNameArr || newNameArr.length === 0 || !newNameArr[0]) {
                    this.resetState();
                    return;
                }
                construct = this.buildPointConstruction(sS.name, lS.name, lS.elType);
                construct.name = newNameArr[0];
                break;
            }
            case 'RenderCircleShape': {
                const circleElement = sS as RenderCircleShape;
                const origCenter = ctrl.rendererCtrl.elementAt(circleElement.centerPointIdx) as RenderVertex;
                const [symCenterCoords] = getSymmetricPointCoords([origCenter.coords]);
                const tempSymCenter: RenderVertex = { ...tempVertexProps, coords: symCenterCoords, relIndex: -1 };
                const newCircleNameArr = (
                    await requestElementNames(ctrl, nt, [
                        {
                            objName: 'Đường Tròn',
                            originElement: [tempSymCenter],
                            pickName: pickPointName,
                            namesToAvoid: [],
                        },
                    ])
                )[0];
                if (!newCircleNameArr || newCircleNameArr.length === 0 || !newCircleNameArr[0]) {
                    this.resetState();
                    return;
                }
                construct = this.buildCircleConstruction(sS.name, lS.name, lS.elType);
                construct.name = newCircleNameArr[0];
                break;
            }
            case 'RenderEllipseShape': {
                const ellipseElement = sS as RenderEllipseShape;
                const origF1 = ctrl.rendererCtrl.elementAt(ellipseElement.f1Idx) as RenderVertex;
                const origF2 = ctrl.rendererCtrl.elementAt(ellipseElement.f2Idx) as RenderVertex;

                const symmetricCoords = getSymmetricPointCoords([origF1.coords, origF2.coords]);
                const [symF1Coords, symF2Coords] = symmetricCoords;

                const tempSymmetricFoci = [
                    { ...tempVertexProps, coords: symF1Coords, relIndex: -1 },
                    { ...tempVertexProps, coords: symF2Coords, relIndex: -2 },
                ];

                const newEllipseNameArr = await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Elip',
                        originElement: tempSymmetricFoci,
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ]);

                if (!newEllipseNameArr || newEllipseNameArr.length < 2 || newEllipseNameArr.some(name => !name)) {
                    this.resetState();
                    return;
                }

                construct = this.buildEllipseConstruction(sS.name, lS.name, lS.elType);
                construct.name = newEllipseNameArr.join('');
                break;
            }
            case 'RenderPolygon': {
                const polygonElement = sS as RenderPolygon;
                const originalVertices = polygonElement.faces.map(
                    faceRelIdx => ctrl.rendererCtrl.elementAt(faceRelIdx) as RenderVertex
                );
                const origVerticesCoords = originalVertices.map(v => v.coords);
                const symVerticesCoords = getSymmetricPointCoords(origVerticesCoords);

                // For naming the polygon object itself, use its first symmetric vertex as a placeholder
                const tempPolygonPlaceholder: RenderVertex[] = symVerticesCoords.map((coords, index) => ({
                    ...tempVertexProps,
                    coords: coords,
                    relIndex: -index,
                }));
                const newPolygonNameArr = await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Đa Giác',
                        originElement: tempPolygonPlaceholder,
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ]);
                if (!newPolygonNameArr || newPolygonNameArr.length === 0 || !newPolygonNameArr[0]) {
                    this.resetState();
                    return;
                }

                construct = this.buildPolygonConstruction(sS.name, lS.name, lS.elType);
                construct.name = newPolygonNameArr.join(''); // Name the polygon object
                break;
            }
            case 'RenderSectorShape': {
                const sectorElement = sS as RenderSectorShape;
                const origCenter = ctrl.rendererCtrl.elementAt(sectorElement.centerPointIdx) as RenderVertex;
                const origStart = ctrl.rendererCtrl.elementAt(sectorElement.startPointIdx) as RenderVertex;
                const origEnd = ctrl.rendererCtrl.elementAt(sectorElement.endPointIdx) as RenderVertex;

                // For naming the sector object itself, use its symmetric center, start, and end as placeholders
                const [symCenterCoords, symStartCoords, symEndCoords] = getSymmetricPointCoords([
                    origCenter.coords,
                    origStart.coords,
                    origEnd.coords,
                ]);

                const tempSectorPlaceholders: RenderVertex[] = [
                    { ...tempVertexProps, coords: symCenterCoords, relIndex: -1 },
                    { ...tempVertexProps, coords: symStartCoords, relIndex: -2 },
                    { ...tempVertexProps, coords: symEndCoords, relIndex: -3 },
                ];

                const newSectorNameArr = await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Cung',
                        originElement: tempSectorPlaceholders,
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ]);

                if (!newSectorNameArr || newSectorNameArr.length < 3 || newSectorNameArr.some(name => !name)) {
                    this.resetState();
                    return;
                }

                construct = this.buildCircularSectorConstruction(sS.name, lS.name, lS.elType);
                construct.name = newSectorNameArr.join(''); // Combine names for the sector object
                break;
            }
            default:
                this.resetState(); // Reset if type not handled
                return;
        }

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo hình đối xứng qua đường thẳng',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(ctrl.state.globalId, [
                        {
                            construction: construct,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                this.resetState();
            }
        );
    }

    private buildCircleConstruction(
        circleName: string,
        lineStartName: string,
        lineStartType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Element/SymmetryThroughLineEC', 'Circle', 'Circle');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aCircle',
                optional: false,
                tplStrLangId: 'tpl-SymmetryCircle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: circleName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildPointConstruction(
        originPointName: string,
        lineStartName: string,
        lineStartType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Element/SymmetryThroughLineEC', 'Point', 'Point');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-SymmetryPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: originPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineConstruction(
        lineName: string,
        lineType: string,
        lineStartName: string,
        lineStartType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Element/SymmetryThroughLineEC',
            lineType as GeoObjectType,
            'Line'
        ); // Use specific lineType

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-SymmetryLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
                dataTypes: {
                    name: lineType,
                },
            },
        ];

        return construction;
    }

    private buildEllipseConstruction(
        ellipseName: string,
        lineStartName: string,
        lineStartType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Element/SymmetryThroughLineEC', 'Ellipse', 'Ellipse');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'anEllipse',
                optional: false,
                tplStrLangId: 'tpl-SymmetryEllipse',
                params: {
                    name: {
                        type: 'singleValue',
                        value: ellipseName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildCircularSectorConstruction(
        sectorName: string,
        lineStartName: string,
        lineStartType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Element/SymmetryThroughLineEC',
            'CircularSector',
            'CircularSector'
        );

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aCircularSector',
                optional: false,
                tplStrLangId: 'tpl-SymmetryCircularSector',
                params: {
                    name: {
                        type: 'singleValue',
                        value: sectorName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildPolygonConstruction(
        polygonName: string,
        lineStartName: string,
        lineStartType: string // Original polygon's elType
    ): GeoElConstructionRequest {
        // The construction request should use the *original* polygon's specific type if needed by backend,
        // or a generic 'Polygon' if the construction is general.
        // For symmetry, the output shape type is usually the same as input.
        const symmetricPolygonElType = this.symmetricShape.elType as GeoObjectType; // e.g., 'Square', 'Triangle'
        const construction = new GeoElConstructionRequest(
            'Element/SymmetryThroughLineEC',
            symmetricPolygonElType,
            'Polygon'
        );

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPolygon',
                optional: false,
                tplStrLangId: 'tpl-SymmetryPolygon',
                params: {
                    name: {
                        type: 'singleValue',
                        value: polygonName, // Name of the original polygon
                    },
                },
                dataTypes: {
                    // Specify the type of the original polygon being referenced
                    name: this.symmetricShape.elType,
                },
            },
        ];

        return construction;
    }

    private buildVectorConstruction(
        vectorName: string,
        lineStartName: string,
        lineStartType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Element/SymmetryThroughLineEC', 'VectorVi', 'Vector');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aVector',
                optional: false,
                tplStrLangId: 'tpl-SymmetryVector',
                params: {
                    name: {
                        type: 'singleValue',
                        value: vectorName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildRayConstruction(
        rayName: string,
        lineStartName: string,
        lineStartType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Element/SymmetryThroughLineEC', 'Ray', 'Ray');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aRay',
                optional: false,
                tplStrLangId: 'tpl-SymmetryRay',
                params: {
                    name: {
                        type: 'singleValue',
                        value: rayName,
                    },
                },
            },
        ];

        return construction;
    }
}
