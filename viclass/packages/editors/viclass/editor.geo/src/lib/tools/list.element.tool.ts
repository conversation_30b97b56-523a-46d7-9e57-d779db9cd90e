import {
    ChangeToolEventData,
    DefaultEventData,
    reliableSaveCmdMeta,
    SelectionEvent,
    VEventListener,
    ViewportContentEvent,
} from '@viclass/editor.core';
import { CmdTypeProto, ElRenderPropsProto } from '@viclass/proto/editor.geo';
import { Subscription } from 'rxjs';
import { syncRenderCommands, UpdateElementsPropCmd } from '../cmd';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { ElementItem, ElementItemAction, GeoRenderElement, ListElementToolState, NOT_SET_VALUE } from '../model';
import { GeometryToolType, GeoToolEventData } from '../model/geo.models';
import { GeoDocCtrl, GeoHitContextDetails, GeoSelectHitContext } from '../objects';
import { GeometryTool } from './geo.tool';
import { addRemoveElementHistoryItem, getFocusDocCtrl } from './util.tool';

export class ListElementTool extends GeometryTool<ListElementToolState> {
    readonly toolType: GeometryToolType = 'ListElementTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
    }

    override onFocus() {}
    override onBlur() {}

    protected override async processChangeToolEvent(event: GeoToolEventData): Promise<GeoToolEventData> {
        const changeEvent = event as ChangeToolEventData<GeometryToolBar, GeometryToolType>;

        // Handle update doc state
        if (changeEvent.changes?.has('action')) {
            const doc = getFocusDocCtrl(this.editor, event.source.viewport.id);
            const action = changeEvent.changes.get('action').currentValue as ElementItemAction;

            switch (action.actionType) {
                case 'hide': {
                    this.setHidingElement(doc, action.relIdx, true);
                    const isSelected = doc.selectedElements.findIndex(rel => rel.relIndex == action.relIdx) > -1;
                    if (isSelected) {
                        const ctx = <GeoSelectHitContext>{
                            doc: doc,
                            hitDetails: <GeoHitContextDetails>{
                                hitId: action.relIdx,
                                el: doc.rendererCtrl.elementAt(action.relIdx),
                            },
                        };
                        this.editor.removeSelectedElement(ctx);
                    }
                    break;
                }
                case 'show': {
                    this.setHidingElement(doc, action.relIdx, false);
                    break;
                }
                case 'highlight': {
                    this.editor.highlight(<GeoSelectHitContext>{
                        doc: doc,
                        hitDetails: <GeoHitContextDetails>{
                            hitId: action.relIdx,
                            el: doc.rendererCtrl.elementAt(action.relIdx),
                        },
                    });
                    break;
                }
                case 'remove-highlight': {
                    this.editor.removeHighlight(<GeoSelectHitContext>{
                        doc: doc,
                        hitDetails: <GeoHitContextDetails>{
                            hitId: action.relIdx,
                            el: doc.rendererCtrl.elementAt(action.relIdx),
                        },
                    });
                    break;
                }
                case 'select':
                case 'select-multiple': {
                    const isSelected = doc.selectedElements.findIndex(rel => rel.relIndex == action.relIdx) > -1;
                    const ctx = <GeoSelectHitContext>{
                        doc: doc,
                        hitDetails: <GeoHitContextDetails>{
                            hitId: action.relIdx,
                            el: doc.rendererCtrl.elementAt(action.relIdx),
                        },
                    };
                    if (isSelected) {
                        if (action.actionType == 'select') {
                            this.editor.clearSelectedElInDoc(doc);
                        } else if (action.actionType == 'select-multiple') {
                            this.editor.removeSelectedElement(ctx);
                        }
                    } else {
                        if (action.actionType == 'select') {
                            this.editor.clearSelectedElInDoc(doc);
                            this.editor.selectElement(ctx, true);
                        } else if (action.actionType == 'select-multiple') {
                            this.editor.selectElement(ctx, true);
                        }
                    }
                    break;
                }
                case 'delete': {
                    const { elIdxes, relIdxes } = await this.editor.geoGateway.removeRenderElements(
                        doc.state.globalId,
                        [action.relIdx]
                    );
                    const renderElements = relIdxes.map(relIdx => {
                        const rel = doc.rendererCtrl.elementAt(relIdx);
                        rel.deleted = true;
                        return rel;
                    });

                    syncRenderCommands(renderElements, doc);
                    addRemoveElementHistoryItem(doc, elIdxes, renderElements);
                    break;
                }
                case 'setting': {
                    await this.toolbar.blur('ListElementTool');
                    await this.toolbar.focus('UpdatePropTool');
                    const isSelected = doc.selectedElements.findIndex(rel => rel.relIndex == action.relIdx) > -1;
                    if (!isSelected) {
                        const ctx = <GeoSelectHitContext>{
                            doc: doc,
                            hitDetails: <GeoHitContextDetails>{
                                hitId: action.relIdx,
                                el: doc.rendererCtrl.elementAt(action.relIdx),
                            },
                        };
                        this.editor.selectElement(ctx);
                    }
                    break;
                }
                default:
                    return event;
            }
        }

        return event;
    }

    private setHidingElement(doc: GeoDocCtrl, relIdx: number, hidden: boolean) {
        const meta = reliableSaveCmdMeta(
            this.toolbar.viewport,
            doc.state,
            doc.state.id,
            doc.state.id,
            CmdTypeProto.UPDATE_ELS_PROP
        );

        const cmd = new UpdateElementsPropCmd(meta);
        const elRenderProps = cmd.state.getElRenderProps() ?? new ElRenderPropsProto();
        cmd.state.setRelIndexList([relIdx]);
        elRenderProps.setHidden(hidden);
        cmd.state.setElRenderProps(elRenderProps);
        this.editor.cmdChannel.receive(cmd);
    }

    override onAttachViewport() {
        this.toolbar.viewport.registerContentEventListener(this.viewportContentEventListener);
    }

    override onDetachViewport() {
        this.toolbar.viewport.unregisterContentEventListener(this.viewportContentEventListener);
    }

    override onEnable(): void {
        super.onEnable();

        if (this.toolbar.viewport) {
            this.toolState.editMode = this.toolbar.viewport.mode === 'EditMode';
            this.toolbar.update('ListElementTool', this.toolState);
        }
    }

    private readonly viewportContentEventListener = new (class ViewportContentEventListener
        implements VEventListener<ViewportContentEvent>
    {
        private docCtrl: GeoDocCtrl;
        private elementChangeListener: VEventListener<DefaultEventData<string, any>>;
        private selectedElementsSub: Subscription;

        constructor(private tool: ListElementTool) {}

        onUnregister(): void {
            if (this.elementChangeListener)
                this.docCtrl?.rendererCtrl?.unregisterElementChange(this.elementChangeListener);
            if (this.selectedElementsSub) this.selectedElementsSub.unsubscribe();
        }

        onEvent(eventData: ViewportContentEvent): Promise<ViewportContentEvent> | ViewportContentEvent {
            if (this.tool.toolbar.viewport.id !== eventData.source.id) return eventData;

            const selectionEvent = eventData.state.originalEvent as SelectionEvent;
            const doc = selectionEvent.source.curDocSelection?.[0]?.doc;
            if (!doc || doc.editor != this.tool.editor) return eventData;

            const geoDoc = doc as GeoDocCtrl;

            if (doc != this.docCtrl) {
                if (this.elementChangeListener) {
                    this.docCtrl.rendererCtrl.unregisterElementChange(this.elementChangeListener);
                    delete this.elementChangeListener;
                }
                if (this.selectedElementsSub) {
                    this.selectedElementsSub.unsubscribe();
                    delete this.selectedElementsSub;
                }
                this.docCtrl = geoDoc;
                this.elementChangeListener = ListElementTool.ElementChangeListener(this.tool, geoDoc);
                this.selectedElementsSub = this.docCtrl.selectedElements$.subscribe(e => {
                    this.elementChangeListener.onEvent(new DefaultEventData('selectElement', ''));
                });
                this.docCtrl.rendererCtrl.registerElementChange(this.elementChangeListener);
            }
            const selected = geoDoc.selectedElements.map(rel => rel.relIndex);
            const highlighted = geoDoc.rendererCtrl.highlightingElements().map(rel => rel.relIndex);
            const toolState = this.tool.toolState;
            toolState.elements = geoDoc.rendererCtrl.usableElements.map(e => {
                const depIdxes = [];

                if (e.lineRelIdxes) depIdxes.push(...e.lineRelIdxes);
                if (e.arcRelIdx && e.arcRelIdx != NOT_SET_VALUE) depIdxes.push(e.arcRelIdx);
                if (e.vertexRelIdxes) depIdxes.push(...e.vertexRelIdxes);

                return <ElementItem>{
                    name: e.name,
                    relIdx: e.relIndex,
                    elType: e.elType,
                    relType: e.type,
                    hidden: e.renderProp.hidden,
                    highlighted: highlighted.includes(e.relIndex),
                    selected: selected.includes(e.relIndex),
                    depIdxes: depIdxes.filter(i => i != null),
                };
            });
            toolState.editMode = eventData.source.mode === 'EditMode';
            this.tool.toolbar.update('ListElementTool', toolState);
            return eventData;
        }
    })(this);

    private static ElementChangeListener(
        tool: ListElementTool,
        doc: GeoDocCtrl
    ): VEventListener<DefaultEventData<string, GeoRenderElement>> {
        return new (class implements VEventListener<DefaultEventData<string, GeoRenderElement>> {
            onUnregister(): void {}

            onEvent(
                eventData: DefaultEventData<string, GeoRenderElement>
            ): Promise<DefaultEventData<string, GeoRenderElement>> | DefaultEventData<string, GeoRenderElement> {
                const toolState = tool.toolState;
                const selected = doc.selectedElements.map(e => e.relIndex);
                const highlighted = doc.rendererCtrl.highlightingElements().map(rel => rel.relIndex);
                toolState.elements = doc.rendererCtrl.usableElements.map(e => {
                    const depIdxes = [];

                    if (e.lineRelIdxes) depIdxes.push(...e.lineRelIdxes);
                    if (e.arcRelIdx && e.arcRelIdx != NOT_SET_VALUE) depIdxes.push(e.arcRelIdx);
                    if (e.vertexRelIdxes) depIdxes.push(...e.vertexRelIdxes);

                    return <ElementItem>{
                        name: e.name,
                        relIdx: e.relIndex,
                        elType: e.elType,
                        relType: e.type,
                        hidden: e.renderProp.hidden,
                        highlighted: highlighted.includes(e.relIndex),
                        selected: selected.includes(e.relIndex),
                        depIdxes: depIdxes.filter(i => i != null),
                    };
                });
                tool.toolbar.update('ListElementTool', toolState);
                return eventData;
            }
        })();
    }
}
