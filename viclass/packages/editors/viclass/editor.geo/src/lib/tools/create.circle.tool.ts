﻿import { UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderCircle, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pCircle, pCircleShape, PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { repeat, RepeatSelector, SelectableType, SelectedVertex, vertexS, VertexSelector } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    assignNames,
    defaultNonUIPointerEventHandler,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    remoteConstruct,
} from './util.tool';

export class CreateCircleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateCircleTool';
    protected objNameDisplay = 'Đường tròn';

    vertSel: VertexSelector;
    declare selLogic: RepeatSelector<SelectableType>;
    pQ = new PreviewQueue();
    renderCircle: RenderCircle;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.selLogic = repeat(vertexS(this.pQ, this.pointerHandler.cursor), {
            count: 2,
            onComplete: this.performConstruction.bind(this, this.objNameDisplay),
        });
    }

    override resetState() {
        this.selLogic.reset();
        super.resetState();
    }
    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    /**
     * Handle a pointer-event “trial” selection and push preview geometry.
     *
     * Workflow
     * --------
     * 1. Ask `selLogic` to pick elements under the pointer.
     * 2. If **exactly two vertices** are returned:
     *    - Build a preview `RenderCircle` (ID –20).
     *    - Build its paired `RenderCircleShape` (ID –21, linked to the circle via `arcRelIdx = -20`).
     *    - Queue both previews in `pQ` so they are rendered as a temporary outline.
     * 3. Flush the preview queue so the user immediately sees feedback.
     *
     * @param event  Low-level pointer data coming from the UI layer.
     * @param ctrl   Current `GeoDocCtrl` giving access to document state & renderer.
     */
    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        // Exactly two points → preview a circle + its filled shape.
        if (selected && selected.length == 2) {
            const v1 = ((selected[0] as Array<any>).length == 2 ? selected[0][1] : selected[0][0]) as RenderVertex;
            const v2 = ((selected[1] as Array<any>).length == 2 ? selected[1][1] : selected[1][0]) as RenderVertex;
            this.renderCircle = pCircle(ctrl, -20, v1, v2);

            const renderCircleShape = pCircleShape(ctrl, -21, v1, this.renderCircle, v2);
            this.pQ.add(renderCircleShape);
        }

        this.pQ.flush(ctrl);
    }

    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    private buildCircleWithRadiusLineSegmentConstruction(
        name: string,
        centerName: string,
        lineName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Circle/CircleEC', 'Circle', 'CenterAndRadiusLineSegment');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-CenterCircle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: centerName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-RadiusLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
        ];

        return construction;
    }

    protected async performConstruction(objName: string, selector: RepeatSelector<SelectedVertex>, ctrl: GeoDocCtrl) {
        const { pcs, points } = await assignNames(
            ctrl,
            selector.selected,
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Bán kính',
            objName,
            this.renderCircle
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        const lineSegmentName = `${points[0].name}${points[1].name}`;

        const circleConstruction = this.buildCircleWithRadiusLineSegmentConstruction(
            this.renderCircle.name,
            points[0].name,
            lineSegmentName
        );

        try {
            await remoteConstruct(ctrl, circleConstruction, pcs, this.editor.geoGateway, 'đường tròn');
        } finally {
            this.resetState();
        }
    }
}
