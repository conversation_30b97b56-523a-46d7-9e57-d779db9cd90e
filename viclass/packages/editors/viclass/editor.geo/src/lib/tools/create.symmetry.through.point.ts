import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderCircleShape,
    RenderEllipseShape,
    RenderLine,
    RenderPolygon,
    RenderSectorShape,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoObjectType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { calculateSymmetricPointsThroughMiddlePoint, createSymmetry } from './symmetry.tool.utils';
import {
    addHistoryItemFromConstructionResponse,
    buildPreviewVertexRenderProp,
    handleIfPointerNotInError,
    pickPointName,
    requestElementNames,
} from './util.tool';

/**
 *
 * <AUTHOR>
 */
export class CreateSymmetricThroughPointTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateSymmetricThroughPointTool';

    private point: RenderVertex; // This is the point of symmetry
    private symmetricShape: GeoRenderElement; // This is the shape to be made symmetric
    private removePreviewFunc: Function = null;
    private isPointerDown = false;
    private selectionStage = 0; // 0: nothing selected, 1: first element selected

    protected override readonly filterElementFunc = (el: GeoRenderElement) =>
        ((el.type == 'RenderVertex' && !this.point) || !this.symmetricShape) && el.type != 'RenderAngle';

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateSymmetricThroughPointTool> =
        new PotentialSelectionDelegator(this);

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // pointerdown events
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', keys: ['shift'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // pointerup events
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', keys: ['shift'], button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },

            // pointermove events
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointermove', button: 0, keys: ['shift'], pointerTypes: pointerTypeMouse },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.point = undefined;
        this.symmetricShape = undefined;
        this.isPointerDown = false;
        this.selectionStage = 0;
        if (this.removePreviewFunc) {
            this.removePreviewFunc();
            this.removePreviewFunc = null;
        }
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }

        event.nativeEvent.preventDefault();
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        this.potentialSelectionDelegator.clearPotential();
        if (this.selectionStage === 0) {
            await this.onFirstElementSelection(event);
        } else if (this.selectionStage === 1) {
            await this.onFinalElementSelection(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onFirstElementSelection(event: GeoPointerEvent) {
        const { ctrl, hitCtx, hitEl } = this.posAndCtrl(event, false);

        if (!this.symmetricShape && hitEl) {
            this.symmetricShape = hitEl;
            ctrl.editor.selectElement(hitCtx, true);
        }
        this.selectionStage = 1;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onFinalElementSelection(event: GeoPointerEvent) {
        const { ctrl, hitEl, hitCtx } = this.posAndCtrl(event, false);
        if (!this.point && hitEl.type == 'RenderVertex') {
            this.point = hitEl as RenderVertex;
            ctrl.editor.selectElement(hitCtx, true);
        }

        if (this.point && this.symmetricShape) {
            await this.createSymmetricObject(ctrl);
        }
    }

    private onPointerMove(event: GeoPointerEvent) {
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        const { ctrl, hitEl, renderer } = this.posAndCtrl(event);

        // Clear previous preview
        if (this.isPointerDown && !this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;

        if (this.selectionStage == 0) {
            if (hitEl?.type == 'RenderVertex') this.symmetricShape = hitEl;
            else this.symmetricShape = null;
            return;
        }

        if (this.selectionStage == 1) {
            if (this.removePreviewFunc != null) {
                this.removePreviewFunc();
                this.removePreviewFunc = null;
            }
            if (hitEl?.type == 'RenderVertex') {
                this.point = hitEl as RenderVertex;
                const shapeToMakeSymmetric = this.symmetricShape;

                this.removePreviewFunc = createSymmetry(
                    shapeToMakeSymmetric,
                    ctrl,
                    renderer,
                    calculateSymmetricPointsThroughMiddlePoint(this.point.coords)
                );
            } else {
                this.point = null;
            }
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async createSymmetricObject(ctrl: GeoDocCtrl) {
        let construct: GeoElConstructionRequest;
        const sS = this.symmetricShape;
        const pS = this.point;

        const getSymmetricPointCoords = (coords: number[][]) =>
            calculateSymmetricPointsThroughMiddlePoint(pS.coords)(coords);

        const tempVertexProps = {
            name: undefined,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            usable: true,
            valid: true,
        } as const;
        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
        switch (sS.type) {
            case 'RenderLine':
            case 'RenderLineSegment':
            case 'RenderRay':
            case 'RenderVector': {
                const lineElement = sS as RenderLine;

                const [origP1, origP2] = [
                    ctrl.rendererCtrl.elementAt(lineElement.startPointIdx) as RenderVertex,
                    lineElement.endPointIdx !== null
                        ? (ctrl.rendererCtrl.elementAt(lineElement.endPointIdx) as RenderVertex)
                        : null,
                ];

                const symmetricCoords = getSymmetricPointCoords(
                    origP2 ? [origP1.coords, origP2.coords] : [origP1.coords]
                );
                const [symP1Coords, symP2Coords] = symmetricCoords;

                const tempSymmetricPoints = [
                    { ...tempVertexProps, coords: symP1Coords, relIndex: -1 },
                    symP2Coords ? { ...tempVertexProps, coords: symP2Coords, relIndex: -2 } : null,
                ].filter(Boolean) as RenderVertex[];

                const newPointNames = (
                    await requestElementNames(ctrl, nt, [
                        {
                            objName: 'Đối xứng của',
                            originElement: tempSymmetricPoints,
                            pickName: pickPointName,
                            namesToAvoid: [origP1.name, origP2?.name].filter(Boolean),
                        },
                    ])
                )[0];

                if (newPointNames.length < tempSymmetricPoints.length || newPointNames.some(name => !name)) {
                    this.resetState();
                    return;
                }

                construct = this.buildLineConstruction(sS.name, sS.elType, pS.name);
                construct.name = newPointNames.join('');
                break;
            }
            case 'RenderVertex': {
                const originalPoint = sS as RenderVertex;
                const [newSymmetricPointCoords] = getSymmetricPointCoords([originalPoint.coords]);
                const tempSymmetricVertex: RenderVertex = {
                    ...tempVertexProps,
                    coords: newSymmetricPointCoords,
                    relIndex: -1,
                };

                const newNameArr = (
                    await requestElementNames(ctrl, nt, [
                        {
                            objName: 'Điểm Đối Xứng Qua Điểm',
                            originElement: [tempSymmetricVertex],
                            pickName: pickPointName,
                            namesToAvoid: [originalPoint.name].filter(n => !!n),
                        },
                    ])
                )[0];
                if (!newNameArr || newNameArr.length === 0 || !newNameArr[0]) {
                    this.resetState();
                    return;
                }
                construct = this.buildPointConstruction(sS.name, pS.name);
                construct.name = newNameArr[0];
                break;
            }
            case 'RenderCircleShape': {
                const circleElement = sS as RenderCircleShape;
                const origCenter = ctrl.rendererCtrl.elementAt(circleElement.centerPointIdx) as RenderVertex;
                const [symCenterCoords] = getSymmetricPointCoords([origCenter.coords]);
                const tempSymCenter: RenderVertex = { ...tempVertexProps, coords: symCenterCoords, relIndex: -1 };
                const newCircleNameArr = (
                    await requestElementNames(ctrl, nt, [
                        {
                            objName: 'Đối xứng của',
                            originElement: [tempSymCenter],
                            pickName: pickPointName,
                            namesToAvoid: [origCenter.name].filter(n => !!n),
                        },
                    ])
                )[0];
                if (!newCircleNameArr || newCircleNameArr.length === 0 || !newCircleNameArr[0]) {
                    this.resetState();
                    return;
                }
                construct = this.buildCircleConstruction(sS.name, pS.name);
                construct.name = newCircleNameArr[0];
                break;
            }
            case 'RenderEllipseShape': {
                const ellipseElement = sS as RenderEllipseShape;
                const origF1 = ctrl.rendererCtrl.elementAt(ellipseElement.f1Idx) as RenderVertex;
                const origF2 = ctrl.rendererCtrl.elementAt(ellipseElement.f2Idx) as RenderVertex;

                const symmetricCoords = getSymmetricPointCoords([origF1.coords, origF2.coords]);
                const [symF1Coords, symF2Coords] = symmetricCoords;

                const tempSymmetricFoci = [
                    { ...tempVertexProps, coords: symF1Coords, relIndex: -1 },
                    { ...tempVertexProps, coords: symF2Coords, relIndex: -2 },
                ];

                const newEllipseNameArr = (
                    await requestElementNames(ctrl, nt, [
                        {
                            objName: 'Đối xứng của',
                            originElement: tempSymmetricFoci,
                            pickName: pickPointName,
                            namesToAvoid: [origF1?.name, origF2?.name].filter(n => !!n),
                        },
                    ])
                )[0];

                if (!newEllipseNameArr || newEllipseNameArr.length < 2 || newEllipseNameArr.some(name => !name)) {
                    this.resetState();
                    return;
                }

                construct = this.buildEllipseConstruction(sS.name, pS.name);
                construct.name = newEllipseNameArr.join('');
                break;
            }
            case 'RenderPolygon': {
                const polygonElement = sS as RenderPolygon;
                const originalVertices = polygonElement.faces.map(
                    faceRelIdx => ctrl.rendererCtrl.elementAt(faceRelIdx) as RenderVertex
                );
                const origVerticesCoords = originalVertices.map(v => v.coords);
                const symVerticesCoords = getSymmetricPointCoords(origVerticesCoords);

                // For naming the polygon object itself, use its first symmetric vertex as a placeholder
                const tempPolygonPlaceholder: RenderVertex[] = symVerticesCoords.map((coords, index) => ({
                    ...tempVertexProps,
                    coords: coords,
                    relIndex: -index,
                }));
                const newPolygonNameArr = (
                    await requestElementNames(ctrl, nt, [
                        {
                            objName: 'Đối xứng của',
                            originElement: tempPolygonPlaceholder,
                            pickName: pickPointName,
                            namesToAvoid: [sS.name, ...originalVertices.map(v => v.name)].filter(n => !!n),
                        },
                    ])
                )[0];
                if (!newPolygonNameArr || newPolygonNameArr.length === 0 || !newPolygonNameArr[0]) {
                    this.resetState();
                    return;
                }

                construct = this.buildPolygonConstruction(sS.name, sS.elType as string, pS.name);
                construct.name = newPolygonNameArr.join(''); // Name the polygon object
                break;
            }
            case 'RenderSectorShape': {
                const sectorElement = sS as RenderSectorShape;
                const origCenter = ctrl.rendererCtrl.elementAt(sectorElement.centerPointIdx) as RenderVertex;
                const origStart = ctrl.rendererCtrl.elementAt(sectorElement.startPointIdx) as RenderVertex;
                const origEnd = ctrl.rendererCtrl.elementAt(sectorElement.endPointIdx) as RenderVertex;

                // For naming the sector object itself, use its symmetric center, start, and end as placeholders
                const [symCenterCoords, symStartCoords, symEndCoords] = getSymmetricPointCoords([
                    origCenter.coords,
                    origStart.coords,
                    origEnd.coords,
                ]);

                const tempSectorPlaceholders: RenderVertex[] = [
                    { ...tempVertexProps, coords: symCenterCoords, relIndex: -1 },
                    { ...tempVertexProps, coords: symStartCoords, relIndex: -2 },
                    { ...tempVertexProps, coords: symEndCoords, relIndex: -3 },
                ];

                const newSectorNameArr = (
                    await requestElementNames(ctrl, nt, [
                        {
                            objName: 'Đối xứng của',
                            originElement: tempSectorPlaceholders,
                            pickName: pickPointName,
                            namesToAvoid: [sS.name, origCenter?.name, origStart?.name, origEnd?.name].filter(n => !!n),
                        },
                    ])
                )[0];

                if (!newSectorNameArr || newSectorNameArr.length < 3 || newSectorNameArr.some(name => !name)) {
                    this.resetState();
                    return;
                }

                construct = this.buildCircularSectorConstruction(sS.name, pS.name);
                construct.name = newSectorNameArr.join(''); // Combine names for the sector object
                break;
            }
            default:
                this.resetState(); // Reset if type not handled
                return;
        }

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo hình đối xứng qua điểm',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(ctrl.state.globalId, [
                        {
                            construction: construct,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                this.resetState();
            }
        );
    }
    private buildPointConstruction(originPointName: string, pointStartName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Element/SymmetryThroughPointEC', 'Point', 'Point');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint', // This is the point of symmetry
                optional: false,
                tplStrLangId: 'tpl-MiddlePoint',
                params: { name: { type: 'singleValue', value: pointStartName } },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-SymmetryPoint',
                params: { name: { type: 'singleValue', value: originPointName } },
            },
        ];
        return construction;
    }
    private buildLineConstruction(
        lineName: string, // Original line name
        lineType: string, // Original line type (e.g., LineSegment, Ray)
        pointStartName: string // Name of the point of symmetry
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Element/SymmetryThroughPointEC',
            lineType as GeoObjectType,
            'Line'
        ); // Name for the new symmetric line will be set in createSymmetricObject
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-MiddlePoint',
                params: { name: { type: 'singleValue', value: pointStartName } },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-SymmetryLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName, // Original line name                    },                },                dataTypes: {                    name: lineType,                },            },        ];        return construction;    }    private buildRayConstruction(rayName: string, pointStartName: string): GeoElConstructionRequest {        const construction = new GeoElConstructionRequest('Element/SymmetryThroughPointEC', 'Ray', 'Ray');        // Name for the new symmetric ray will be set in createSymmetricObject        construction.paramSpecs = [            {                indexInCG: 0,                paramDefId: 'aPoint',                optional: false,                tplStrLangId: 'tpl-MiddlePoint',                params: {                    name: {                        type: 'singleValue',                        value: pointStartName,                    },                },            },            {                indexInCG: 1,                paramDefId: 'aRay',                optional: false,                tplStrLangId: 'tpl-SymmetryRay',                params: {                    name: {                        type: 'singleValue',                        value: rayName,
                    },
                },
            },
        ];
        return construction;
    }
    private buildVectorConstruction(vectorName: string, pointStartName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Element/SymmetryThroughPointEC', 'VectorVi', 'Vector');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-MiddlePoint',
                params: { name: { type: 'singleValue', value: pointStartName } },
            },
            {
                indexInCG: 1,
                paramDefId: 'aVector',
                optional: false,
                tplStrLangId: 'tpl-SymmetryVector',
                params: {
                    name: {
                        type: 'singleValue',
                        value: vectorName, // Original vector name
                    },
                },
            },
        ];
        return construction;
    }
    private buildCircleConstruction(circleName: string, pointStartName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Element/SymmetryThroughPointEC', 'Circle', 'Circle');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-MiddlePoint',
                params: { name: { type: 'singleValue', value: pointStartName } },
            },
            {
                indexInCG: 1,
                paramDefId: 'aCircle',
                optional: false,
                tplStrLangId: 'tpl-SymmetryCircle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: circleName, // Original circle name
                    },
                },
            },
        ];
        return construction;
    }
    private buildEllipseConstruction(ellipseName: string, pointStartName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Element/SymmetryThroughPointEC', 'Ellipse', 'Ellipse');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-MiddlePoint',
                params: { name: { type: 'singleValue', value: pointStartName } },
            },
            {
                indexInCG: 1,
                paramDefId: 'anEllipse',
                optional: false,
                tplStrLangId: 'tpl-SymmetryEllipse',
                params: {
                    name: {
                        type: 'singleValue',
                        value: ellipseName, // Original ellipse name
                    },
                },
            },
        ];
        return construction;
    }
    private buildCircularSectorConstruction(sectorName: string, pointStartName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Element/SymmetryThroughPointEC',
            'CircularSector',
            'CircularSector'
        );
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-MiddlePoint',
                params: { name: { type: 'singleValue', value: pointStartName } },
            },
            {
                indexInCG: 1,
                paramDefId: 'aCircularSector',
                optional: false,
                tplStrLangId: 'tpl-SymmetryCircularSector',
                params: {
                    name: {
                        type: 'singleValue',
                        value: sectorName, // Original sector name
                    },
                },
            },
        ];
        return construction;
    }
    private buildPolygonConstruction(
        polygonName: string, // Original polygon name
        polygonElType: string, // Original polygon element type (e.g., 'Polygon', 'Square')
        pointStartName: string // Name of the point of symmetry
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Element/SymmetryThroughPointEC',
            polygonElType as GeoObjectType,
            'Polygon'
        ); // Name for the new symmetric polygon will be set in createSymmetricObject
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-MiddlePoint',
                params: { name: { type: 'singleValue', value: pointStartName } },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPolygon',
                optional: false,
                tplStrLangId: 'tpl-SymmetryPolygon',
                params: {
                    name: {
                        type: 'singleValue',
                        value: polygonName, // Original polygon name
                    },
                },
                dataTypes: {
                    // Specify the type of the original polygon being referenced
                    name: polygonElType,
                },
            },
        ];
        return construction;
    }
}
