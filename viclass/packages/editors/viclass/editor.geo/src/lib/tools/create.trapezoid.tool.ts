import { line, point, vector } from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewVertexRenderProp,
    getPointAndVertex,
    handleIfPointerNotInError,
    isDifferentCoords,
    pickPointName,
    requestElementNames,
} from './util.tool';

export class CreateTrapezoidTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateTrapezoidTool';

    private points: RenderVertex[] = [];
    private isPointerDown = false;
    private clickCount = 0;

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;
    };

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateTrapezoidTool> =
        new PotentialSelectionDelegator(this);

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },

            // move point/line preview
            { event: 'pointermove', pointerTypes: pointerTypeMouse, keys: ['nokey'] },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.isPointerDown = false;
        this.clickCount = 0;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;
        this.clickCount++;

        if (this.clickCount == 1) {
            await this.handleFirstPoint(event);
        } else if (this.clickCount == 2) {
            await this.handleSecondPoint(event);
        } else if (this.clickCount == 3) {
            await this.handleThirdPoint(event);
        } else if (this.clickCount == 4) {
            await this.handleFourthPoint(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        this.potentialSelectionDelegator.clearPotential();
        if (this.clickCount == 4) {
            await this.finalizeTrapezoid(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFirstPoint(event: GeoPointerEvent) {
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points[0] = vertex; // add/update first point

        this.previewTrapezoid(ctrl, [vertex.coords, vertex.coords]);
        this.started = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleSecondPoint(event: GeoPointerEvent) {
        const { ctrl, vertex } = getPointAndVertex(this, event);
        if (isDifferentCoords(vertex.coords, this.points[0].coords)) {
            this.points[1] = vertex; // add/update 2nd point when it does not match the first point
        }
        if (this.points.length !== 2) return;

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];

        this.previewTrapezoid(ctrl, [v1, v2]);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleThirdPoint(event: GeoPointerEvent) {
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points[2] = vertex; // add/update third point

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        const v3 = [vertex.coords[0], vertex.coords[1], 0];

        this.previewTrapezoid(ctrl, [v1, v2, v3]);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFourthPoint(event: GeoPointerEvent) {
        const { ctrl, vertex, coords } = getPointAndVertex(this, event);
        this.points[3] = vertex; // add/update fourth point

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        const v3 = [this.points[2].coords[0], this.points[2].coords[1], 0];

        const p = point(coords[0], coords[1]);
        const p1 = point(v1[0], v1[1]);
        const p2 = point(v2[0], v2[1]);
        const p3 = point(v3[0], v3[1]);

        const linep1p2 = line(p1, p2);
        const parallelLine = line(p3, linep1p2.norm);
        const projection = p.projectionOn(parallelLine);
        let v4 = [projection.x, projection.y, 0.0];

        const p4 = point(v4[0], v4[1]);
        if (p1.equalTo(p2) || p3.equalTo(p4)) return;

        const ve1 = vector(p1, p2).normalize();
        const ve2 = vector(p3, p4).normalize();

        if (ve1.equalTo(ve2)) {
            v4 = v3;
        }

        this.previewTrapezoid(ctrl, [v1, v2, v3, v4]);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeTrapezoid(event: GeoPointerEvent) {
        const { ctrl, docGlobalId } = this.posAndCtrl(event);

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        const v3 = [this.points[2].coords[0], this.points[2].coords[1], 0];
        const v4 = [this.points[3].coords[0], this.points[3].coords[1], 0];

        const p1 = point(v1[0], v1[1]);
        const p2 = point(v2[0], v2[1]);
        const p3 = point(v3[0], v3[1]);
        const p4 = point(v4[0], v4[1]);

        const linep1p2 = line(p1, p2);
        const parallelLine = line(p3, linep1p2.norm);
        const projection = p4.projectionOn(parallelLine);

        // Calculate final coordinates for the fourth point to ensure it forms a valid trapezoid
        const finalV4 = [projection.x, projection.y, 0.0];

        // Check if the trapezoid would be valid (not a parallelogram)
        const ve1 = vector(p1, p2).normalize();
        const ve2 = vector(p3, projection).normalize();

        if (ve1.equalTo(ve2)) {
            // Not a valid trapezoid, show error or return
            return;
        }

        const le = p3.distanceTo(projection)[0];

        // Update the fourth point with the corrected coordinates
        this.points[3] = {
            relIndex: -14,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            coords: finalV4,
            name: undefined,
            usable: true,
            valid: true,
        };
        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
        // Submit construction
        const constructionPoints: GeoElConstructionRequest[] = [];
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Hình Thang',
                    originElement: this.points,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        for (let i = 0; i < this.points.length; i++) {
            const p = this.points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                const constructionPoint = buildPointConstruction(p.name, {
                    x: p.coords[0],
                    y: p.coords[1],
                });
                constructionPoints.push(constructionPoint);
            }
        }

        let constructionPolygon: GeoElConstructionRequest;
        if (this.points.length === 4) {
            // Use FromPoints construction if we have all 4 points
            constructionPolygon = this.buildTrapezoidFromPointsConstruction(this.points.map(p => p.name));
        } else {
            // Use LineSegmentAndOppositeSideLength construction for other cases
            const lineName = `${this.points[0].name}${this.points[1].name}`;
            const trapezoidName = `${lineName}${this.points[2].name}${this.points[3].name}`;
            constructionPolygon = this.buildTrapezoidConstruction(trapezoidName, lineName, le);
        }

        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo hình thang',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(docGlobalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        {
                            construction: constructionPolygon,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length == 0) return;

        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;
            // while pointer down -> handle all 4 cases
            if (this.clickCount === 1) {
                this.handleFirstPoint(event);
            } else if (this.clickCount === 2) {
                this.handleSecondPoint(event);
            } else if (this.clickCount === 3) {
                this.handleThirdPoint(event);
            } else if (this.clickCount === 4) {
                this.handleFourthPoint(event);
            }
        } else {
            // Preview for the current state while not dragging
            const { ctrl, coords } = getPointAndVertex(this, event);

            if (this.points.length === 1) {
                // Preview the line between first point and current position
                const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
                this.previewTrapezoid(ctrl, [v1, coords]);
            } else if (this.points.length === 2) {
                // Preview the triangle
                const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
                const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
                this.previewTrapezoid(ctrl, [v1, v2, coords]);
            } else if (this.points.length >= 3) {
                // Preview the full trapezoid
                this.handleFourthPoint(event);
            }
        }
    }

    private previewTrapezoid(ctrl: GeoDocCtrl, faces: number[][]) {
        // const polygon: PreviewPolygon = {
        //     relIndex: -20,
        //     name: '',
        //     type: 'RenderPolygon',
        //     elType: 'Trapezoid',
        //     faces: faces,
        //     renderProp: buildPreviewPolygonRenderProp(),
        //     usable: true,
        //     valid: true,
        // };
        // syncPreviewCommands(polygon, ctrl);
    }

    private buildTrapezoidConstruction(name: string, lineName: string, length: number): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Trapezoid/TrapezoidEC',
            'Trapezoid',
            'LineSegmentAndOppositeSideLength'
        );
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-OppositeSideLength',
                params: {
                    value: {
                        type: 'singleValue',
                        value: length,
                    },
                },
            },
        ];

        return construction;
    }

    private buildTrapezoidFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Trapezoid/TrapezoidEC', 'Trapezoid', 'FromPoints');
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}
