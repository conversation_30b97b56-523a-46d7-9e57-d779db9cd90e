import { UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderLineSegment } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue, pSector, pSectorShape, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { nPoints, RepeatSelector, SelectedVertex, vert } from '../selectors';
import { getFocusDocCtrl } from './util.tool';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { assignNames, handleIfPointerNotInError, remoteConstruct, pickPointName } from './util.tool';

export class CreateSemicircleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateSemicircleTool';

    declare selLogic: RepeatSelector<SelectedVertex>;
    pQ = new PreviewQueue();
    previewSector: any = null; // Store preview sector for reuse

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();

        this.selLogic = nPoints(this.pQ, this.pointerHandler.cursor, {
            count: 2,
            onComplete: this.performConstruction.bind(this),
        });
    }

    override resetState() {
        this.selLogic.reset();
        this.previewSector = null; // Reset preview sector
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl));

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (selected && selected.length >= 2) {
            // Use vert() to extract vertex from SelectedVertex (supports multiple render shapes)
            const v1 = vert(selected[0]);
            const v2 = vert(selected[1]);

            // Calculate center point coordinates for diameter preview
            const centerCoords = [(v1.coords[0] + v2.coords[0]) / 2, (v1.coords[1] + v2.coords[1]) / 2];

            // Create center point using pVertex utility
            const centerPoint = pVertex(-22, centerCoords);

            // Add preview elements - lines and semicircle
            this.pQ.add(pLine(ctrl, -24, RenderLineSegment, v1, centerPoint));
            this.pQ.add(pLine(ctrl, -23, RenderLineSegment, centerPoint, v2));

            // Store sector for reuse in performConstruction
            this.previewSector = pSector(ctrl, -20, v1, centerPoint, v2);
            this.pQ.add(pSectorShape(ctrl, -21, v1, centerPoint, v2, this.previewSector));
        }

        this.pQ.flush(ctrl);
    }

    private async performConstruction(selector: RepeatSelector<SelectedVertex>, ctrl: GeoDocCtrl) {
        const selected = selector.selected;
        if (!selected || selected.length < 2) {
            this.resetState();
            return;
        }

        // Extract vertices from selection using vert() - supports multiple render shapes
        const point1 = vert(selected[0]);
        const point2 = vert(selected[1]);

        // Calculate center point coordinates and create center point for naming
        const centerCoords = [(point1.coords[0] + point2.coords[0]) / 2, (point1.coords[1] + point2.coords[1]) / 2];
        const centerPoint = pVertex(-1, centerCoords);

        try {
            // Assign names for all 3 points (2 endpoints + center) to get the names
            const { pcs } = await assignNames(
                ctrl,
                [selected[0], centerPoint, selected[1]], // 3 points including center
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Điểm bán nguyệt',
                'bán nguyệt',
                this.previewSector
            );

            if (!pcs) {
                this.resetState();
                return;
            }

            // Get center point name from assigned names
            const centerPointName = centerPoint.name;

            // Build construction with 2 endpoints + center name
            const semicircleConstruction = this.buildSemicircleConstruction(
                this.previewSector.name,
                point1.name,
                point2.name,
                centerPointName
            );

            // Filter pcs to remove center point construction - only pass the 2 endpoints
            const endpointConstructions = pcs.filter(pc => pc.name === point1.name || pc.name === point2.name);

            await remoteConstruct(
                ctrl,
                semicircleConstruction,
                endpointConstructions,
                this.editor.geoGateway,
                'bán nguyệt'
            );
        } finally {
            this.resetState();
        }
    }

    private buildSemicircleConstruction(
        name: string,
        point1Name: string,
        point2Name: string,
        centerPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Semicircle/SemicircleEC', 'Semicircle', 'WithTwoEndpoints');
        construction.name = name; // Semicircle name goes here, not in parameters

        const paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-StartPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: point1Name,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-EndPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: point2Name,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aName',
                optional: true,
                tplStrLangId: 'tpl-CenterPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: centerPointName,
                    },
                },
            },
        ];

        construction.paramSpecs = paramSpecs;
        return construction;
    }
}
