import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { ThenSelector, vert, VertexOnStroke } from '../selectors';
import { GeometryTool } from './geo.tool';
import {
    createLineToolSelLogic,
    executeLineConstruction,
    LineToolConstructionMethods,
} from './parallel_perpendicular.line.tool.utils';
import {
    buildPointConstruction,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    pickPointName,
    requestElementNames,
} from './util.tool';

/**
 * Perpendicular Line Tool - Creates perpendicular lines using selector pattern
 * <AUTHOR>
 */
export class CreatePerpendicularLineTool extends GeometryTool<CommonToolState> implements LineToolConstructionMethods {
    readonly toolType: GeometryToolType = 'CreatePerpendicularLineTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;
    isShowingPreview = false;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedPoint = undefined;
        this.previewLine = undefined;
        this.isShowingPreview = false;
        super.resetState();
    }

    /**
     * Creates the selection logic using utility function
     * Following Pattern: Sequential Selection with Preview Management
     */
    private createSelLogic() {
        this.selLogic = createLineToolSelLogic(
            this.pQ,
            this.pointerHandler,
            async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const selected = selector.selected;
                if (!selected || selected.length < 3) return;

                this.selectedLine = selected[0] as RenderLine;
                this.selectedPoint = vert(selected[1] as RenderVertex | VertexOnStroke);
                const finalVertex = vert(selected[2] as RenderVertex | VertexOnStroke);
                const finalVertexSelection = selected[2];

                // Handle vertex construction if needed
                const { vertexConstructions, namedFinalVertex } = await this.handleVertexConstruction(
                    doc,
                    finalVertex,
                    this.selectedPoint
                );

                // Use new simplified utility function for construction
                await executeLineConstruction(
                    this,
                    doc,
                    {
                        selectedLine: this.selectedLine,
                        selectedPoint: this.selectedPoint,
                        finalVertex: namedFinalVertex,
                        finalVertexSelection: finalVertexSelection,
                        vertexConstructions: vertexConstructions,
                    },
                    true // isPerpendicular = true for perpendicular lines
                );
            }
        );
    }

    /**
     * Handle vertex construction logic using imported utility functions
     */
    private async handleVertexConstruction(
        ctrl: GeoDocCtrl,
        finalVertex: RenderVertex,
        _selectedPoint: RenderVertex
    ): Promise<{ vertexConstructions: GeoElConstructionRequest[]; namedFinalVertex: RenderVertex }> {
        // Check if final vertex needs construction (doesn't have a name and has negative relIndex)
        const needsVertexConstruction = !finalVertex.name && finalVertex.relIndex < 0;
        const vertexConstructions: GeoElConstructionRequest[] = [];
        let namedFinalVertex = finalVertex;

        if (needsVertexConstruction) {
            // Request name for the vertex
            const nt = this.toolbar.getTool('NamingElementTool') as any;
            const inputPointNames = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Điểm',
                        originElement: [finalVertex],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];

            if (!inputPointNames.length) {
                this.resetState();
                throw new Error('No point names provided');
            }

            // Create a new vertex with the assigned name
            namedFinalVertex = {
                ...finalVertex,
                name: inputPointNames[0],
            };

            // Create vertex construction
            const vertexConstruction = buildPointConstruction(namedFinalVertex.name, {
                x: namedFinalVertex.coords[0],
                y: namedFinalVertex.coords[1],
            });
            vertexConstructions.push(vertexConstruction);
        }

        return { vertexConstructions, namedFinalVertex };
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, (event: any) =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        // Continue with selection logic
        const selected = this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (selected && selected.length === 1) {
            // Line selected, store reference
            this.selectedLine = selected[0] as RenderLine;
        } else if (selected && selected.length === 2) {
            // Line and point selected, store references
            this.selectedLine = selected[0] as RenderLine;
            this.selectedPoint = vert(selected[1] as RenderVertex | VertexOnStroke);
            this.isShowingPreview = true;
        } else if (selected && selected.length === 3) {
            // All selections complete
            this.selectedLine = selected[0] as RenderLine;
            this.selectedPoint = vert(selected[1] as RenderVertex | VertexOnStroke);
        }

        // Always flush at the end
        this.pQ.flush(ctrl);
    }

    // Construction methods remain the same but simplified
    buildLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'ThoughPointPerpendicularWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
        ];

        return construction;
    }

    buildLineSegment(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string,
        k: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'PerpendicularWithNewPoint'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    buildLineSegmentWithIntersectLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        intersectionLineName: string,
        intersectionLineType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'ThroughPointSegmentPerpendicularWithLineAndIntersectionLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
