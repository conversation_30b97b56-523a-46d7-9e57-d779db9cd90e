import {
    ChangeToolEventData,
    reliableCmdMeta,
    SelectionEvent,
    VEventListener,
    ViewportContentEvent,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.geo';
import { BehaviorSubject, Subscription } from 'rxjs';
import { UpdateElementNameCmd } from '../cmd';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoRenderElement, RenameElementToolState } from '../model';
import { GeometryToolType, GeoPointerEvent, GeoToolEventData } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { GeometryTool } from './geo.tool';
import { addRenameElementHistoryItem, isValidIdx } from './util.tool';

/**
 * <AUTHOR>
 */
export class RenameElementTool extends GeometryTool<RenameElementToolState> {
    readonly viewportContentEventListener: VEventListener<ViewportContentEvent>;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.viewportContentEventListener = new (class ViewportContentEventListener
            implements VEventListener<ViewportContentEvent>
        {
            curDocSelectedElementsSub: Subscription;

            constructor(private tool: RenameElementTool) {}

            onEvent(eventData: ViewportContentEvent): ViewportContentEvent | Promise<ViewportContentEvent> {
                if (this.tool.toolbar.viewport.id !== eventData.source.id) return eventData;

                this.resetState();

                const selectionEvent = eventData.state.originalEvent as SelectionEvent;
                const doc = selectionEvent.source.curDocSelection?.[0]?.doc;
                if (!doc || doc.editor != this.tool.editor) return eventData;

                if (selectionEvent.source.curDocSelection.length === 1 && doc instanceof GeoDocCtrl) {
                    this.curDocSelectedElementsSub = doc.selectedElements$?.subscribe(selectedElements => {
                        if (selectedElements.length === 1) {
                            const el = selectedElements?.[0];
                            const toolState = this.tool.toolState;
                            toolState.docLocalId = doc.state.id;
                            toolState.relIndex = el.relIndex;
                            toolState.name = el.name;
                            toolState.notProcess = true;
                            toolbar.update('RenameElementTool', toolState);
                        } else this.tool.resetState();
                    });
                }

                return eventData;
            }
            onUnregister() {
                this.resetState();
            }
            resetState() {
                this.tool.resetState();
                this.curDocSelectedElementsSub?.unsubscribe();
                delete this.curDocSelectedElementsSub;
            }
        })(this);
    }

    get toolType(): GeometryToolType {
        return 'RenameElementTool';
    }

    private readonly currentUpdatingNameRequest = new BehaviorSubject<Function>(null);

    /**
     * Processes changes in tool state, particularly handling an element renaming events
     * @param event The geometry tool event data containing state changes
     * @returns The processed event data
     */
    protected override async processChangeToolEvent(event: GeoToolEventData): Promise<GeoToolEventData> {
        // Get an active viewport and cast event to appropriate types
        const activeVp = this.toolbar.viewport;
        const changeEvent = event as ChangeToolEventData<GeometryToolBar, GeometryToolType>;
        const toolState = event.state as RenameElementToolState;

        // Return early if no changes detected
        if (!changeEvent.changes || changeEvent.changes.size <= 0) return event;

        // Validate tool state - return if invalid document ID, element index,
        // or if changes should not be processed
        if (
            toolState.docLocalId == null ||
            !isValidIdx(toolState.relIndex) ||
            (changeEvent.changes.has('notProcess') && changeEvent.changes.get('notProcess').currentValue === true)
        )
            return event;

        // Get a document controller for the active viewport
        const doc = this.getGeoDoc(activeVp, toolState.docLocalId);

        // Handle name change events
        if (changeEvent.changes.has('name')) {
            // Create command metadata with sync disabled initially
            const meta = reliableCmdMeta(activeVp, doc.state.id, doc.state.id, CmdTypeProto.UPDATE_EL_NAME);

            // Create and configure the rename command
            const cmd = new UpdateElementNameCmd(meta);
            const name = changeEvent.changes.get('name').currentValue;
            cmd.setName(name);
            cmd.setRelIndex(toolState.relIndex);

            try {
                // Execute command first
                await this.editor.cmdChannel.receive(cmd);

                // Update server and add to history
                addRenameElementHistoryItem(
                    doc,
                    await this.editor.geoGateway.renameElement(doc.state.globalId, toolState.relIndex, name)
                );
            } catch (err) {
                // On error, revert name change and set validation error
                const el = doc.rendererCtrl.elementAt(toolState.relIndex);
                toolState.name = el.name;
                toolState.validateResult = {
                    valid: false,
                    message: err.message,
                };

                cmd.setName(el.name);
                cmd.setRelIndex(el.relIndex);
                await this.editor.cmdChannel.receive(cmd);
            }
        }

        return event;
    }

    updateState(docLocalId: number, el: GeoRenderElement) {
        const toolState = this.toolState;
        if (docLocalId != null && el) {
            toolState.docLocalId = docLocalId;
            toolState.relIndex = el.relIndex;
            toolState.name = el.name;
            toolState.notProcess = false;
        } else {
            this.resetState();
            return;
        }

        this.toolbar.update('RenameElementTool', toolState);
    }

    override resetState() {
        this.toolState.reset();

        this.toolbar.update('RenameElementTool', this.toolState);
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        return event;
    }

    override onAttachViewport() {
        this.toolbar.viewport.registerContentEventListener(this.viewportContentEventListener);
    }

    override onDetachViewport() {
        this.toolbar.viewport.unregisterContentEventListener(this.viewportContentEventListener);
    }
}
