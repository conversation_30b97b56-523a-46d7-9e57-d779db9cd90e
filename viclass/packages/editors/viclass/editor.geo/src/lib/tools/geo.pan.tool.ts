import {
    DefaultPointerEventData,
    mouseLocation,
    NativeEventTarget,
    PanFeature,
    PointerEventData,
    SupportPanFeature,
} from '@viclass/editor.core';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoPanToolState, UpdatePropToolState } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { GeometryTool } from './geo.tool';
import { validatePointerPos } from './util.tool';
import { UpdatePropTool } from './update.prop.tool';
import { syncUpdateDocStateCommand } from '../cmd';

export class GeoPanTool extends GeometryTool<GeoPanToolState> implements SupportPanFeature {
    constructor(
        editor: GeometryEditor,
        toolbar: GeometryToolBar,
        private panFeature: PanFeature
    ) {
        super(editor, toolbar);
    }

    get toolType(): GeometryToolType {
        return 'GeoPanTool';
    }

    override resetState() {
        this.clear();
    }

    clear() {
        this.toolState.doc = undefined;
        this.started = false;
        this.lastPointerMove = undefined;
    }

    override onBlur() {
        this.clear();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        return event;
    }

    override onAttachViewport() {
        this.panFeature.registerPanHandler(this.toolbar.viewport.id, this.editor.editorType, this);
    }

    isPanHandleAble(docCtrl: GeoDocCtrl, event: PointerEventData<NativeEventTarget<any>>): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;
        try {
            if (event instanceof DefaultPointerEventData) {
                const mousePos = mouseLocation(event);
                if (!validatePointerPos(mousePos, docCtrl)) {
                    return false;
                }
                return true;
            }
        } catch (error) {
            return false;
        }

        return false;
    }

    isPanHandling(docCtrl: GeoDocCtrl) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;
        return this.toolState.doc === docCtrl;
    }

    startPan(docCtrl: GeoDocCtrl) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        this.toolState.doc = docCtrl;
        this.started = true;
    }

    stopPan(docCtrl: GeoDocCtrl) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        if (this.toolState.doc !== docCtrl) return;

        const toolState: UpdatePropToolState = this.toolbar.toolState('UpdatePropTool');
        toolState.docRenderProp = docCtrl.state.docRenderProp;
        toolState.notProcess = false;
        this.toolbar.update('UpdatePropTool', toolState);

        this.clear();
    }

    translateViewpoint(docCtrl: GeoDocCtrl, deltaXInScreen: number, deltaYInScreen: number) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        if (docCtrl !== this.toolState.doc) return;
        const docRenderProp = this.toolState.doc.state.docRenderProp;
        const x =
            docRenderProp.translation[0] +
            this.toolState.doc.rendererCtrl.layerToGeoLength(deltaXInScreen * docCtrl.viewport.zoomLevel);
        const y =
            docRenderProp.translation[1] +
            this.toolState.doc.rendererCtrl.layerToGeoLength(deltaYInScreen * docCtrl.viewport.zoomLevel);

        docCtrl.rendererCtrl.lookAt(x, y);

        syncUpdateDocStateCommand(docCtrl);

        // update toolstate
        this.toolbar.update('GeoPanTool', this.toolState);

        // update doc state
        const updatePropTool = this.toolbar.getTool('UpdatePropTool') as UpdatePropTool;
        const updatePropToolState: UpdatePropToolState = updatePropTool.toolState;
        updatePropToolState.docRenderProp = docRenderProp;
        updatePropToolState.notProcess = false;

        this.toolbar.update('UpdatePropTool', updatePropToolState);
    }
}
