﻿import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { OrSelector, SelectedVertex, vertexS } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { assignNames, getFocusDocCtrl, handleIfPointerNotInError, remoteConstruct } from './util.tool';

export class CreatePointTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreatePointTool';

    private selectionLogic!: OrSelector<SelectedVertex>;
    private pQ = new PreviewQueue();

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();

        this.selectionLogic = vertexS(this.pQ, this.pointerHandler.cursor).setOption(
            'onComplete',
            this.onPointSelected.bind(this)
        );
    }

    override resetState(): void {
        super.resetState();
        this.selectionLogic.reset();
    }

    async onPointSelected(selector: OrSelector<SelectedVertex>, ctrl: GeoDocCtrl) {
        const { pcs } = await assignNames(
            ctrl,
            [selector.selected],
            this.toolbar.getTool('NamingElementTool') as NamingElementTool
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        try {
            await remoteConstruct(ctrl, pcs[0], [], this.editor.geoGateway, 'Điểm');
        } finally {
            this.resetState();
        }
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.selectionLogic.trySelect(event, ctrl))
            );
        else this.selectionLogic.trySelect(event, ctrl);

        this.pQ.flush(ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }
}
