# Tài Liệu Hệ Thống Giải Toán <PERSON>

## Tổng <PERSON>uan

<PERSON>ệ thống giải cung cấp các thuật toán toán học mạnh mẽ cho phương trình đa thức, phép toán số phức và thao tác hệ số hình học. Nó tạo thành nền tảng toán học cho các xây dựng hình học nâng cao và tính toán.

## Kiến Trúc

### Các Thành Phần Giải

```
solving/
├── complex.ts          # Số học số phức
├── poly.solving.ts     # Giải phương trình đa thức
└── coefficients.ts     # Thao tác hệ số ellipse
```

## Hệ Thống Số Phức

### Lớp Complex

Lớp `Complex` cung cấp các phép toán số phức toàn diện:

```typescript
export class Complex {
    static EPSILON: number = 1e-8;
    private _re: number;
    private _im: number;
    
    constructor(re: number = 0, im: number = 0) {
        this._re = re;
        this._im = im;
    }
    
    // Getter
    get real(): number { return this._re; }
    get imag(): number { return this._im; }
    
    // Phép toán cơ bản
    add(n: number | Complex): Complex;
    sub(n: number | Complex): Complex;
    mult(n: number | Complex): Complex;
    div(n: number | Complex): Complex;
}
```

### Tạo Số Phức

```typescript
// Phương thức factory tĩnh
const z1 = Complex.fromRI(3, 4);           // 3 + 4i
const z2 = Complex.fromPolar(5, Math.PI/4); // 5∠45°
const z3 = Complex.fromZ(z1);              // Constructor sao chép

// Xây dựng trực tiếp
const z4 = new Complex(2, -3);             // 2 - 3i
```

### Phép Toán Số Học

```typescript
const z1 = new Complex(3, 4);
const z2 = new Complex(1, 2);

// Số học cơ bản
const sum = z1.add(z2);        // (3+4i) + (1+2i) = 4+6i
const diff = z1.sub(z2);       // (3+4i) - (1+2i) = 2+2i
const product = z1.mult(z2);   // (3+4i) * (1+2i) = -5+10i
const quotient = z1.div(z2);   // (3+4i) / (1+2i) = 2.2-0.4i

// Với số thực
const scaled = z1.mult(2);     // 2 * (3+4i) = 6+8i
const shifted = z1.add(5);     // (3+4i) + 5 = 8+4i
```

### Phép Toán Nâng Cao

```typescript
const z = new Complex(3, 4);

// Độ lớn và pha
const magnitude = z.abs();           // |z| = 5
const phase = z.arg();              // arg(z) = arctan(4/3)

// Lũy thừa và căn
const squared = z.squared();         // z²
const cubed = z.cubed();            // z³
const power = z.pow(2.5);           // z^2.5
const roots = z.roots(3);           // Căn bậc ba của z

// Hàm lượng giác
const exp = z.exp();                // e^z
const log = z.log();                // ln(z)
const sqrt = z.sqrt();              // √z
```

### Phương Thức Tiện Ích

```typescript
const z1 = new Complex(3, 4);
const z2 = new Complex(3, 4.0000001);

// So sánh với dung sai
const isEqual = z1.equals(z2, 1e-6);    // true
const isReal = z1.isReal();              // false
const isZero = Complex.ZERO.isZero();    // true

// Chuyển đổi và hiển thị
const conjugate = z1.conjugate();        // 3 - 4i
const negated = z1.negate();            // -3 - 4i
const string = z1.toString();           // "3 + 4i"
```

## Giải Đa Thức

### Lớp Đa Thức Cơ Sở

```typescript
export class PolyBase {
    protected cf: number[];
    protected all_roots: Complex[];
    
    static getPoly(coefs: number[]): PolyBase {
        // Tự động xác định bậc đa thức và trả về lớp phù hợp
        switch (coefs.length) {
            case 5: return new Quartic(coefs);
            case 4: return new Cubic(coefs);
            case 3: return new Quadratic(coefs);
            case 2: return new Linear(coefs);
            default: throw new Error('Hệ số không hợp lệ');
        }
    }
    
    roots(): Complex[] {
        return [...this.all_roots];
    }
    
    eval(x: number | Complex): Complex {
        // Tính giá trị đa thức tại điểm cho trước
    }
}
```

### Phương Trình Tuyến Tính

Giải phương trình dạng: `a₁x + a₀ = 0`

```typescript
export class Linear extends PolyBase {
    constructor(coefs: number[]) {
        super();
        // coefs = [a1, a0] với a1 ≠ 0
        if (coefs.length !== 2 || coefs[0] === 0) {
            throw new Error('Hệ số không hợp lệ cho phương trình tuyến tính');
        }
        this.cf = coefs.reverse().map(c => c / coefs[0]);
        this.all_roots = [new Complex(-this.cf[0], 0)];
    }
}

// Sử dụng
const linear = new Linear([2, -6]);  // 2x - 6 = 0
const roots = linear.roots();        // [3 + 0i]
```

### Phương Trình Bậc Hai

Giải phương trình dạng: `a₂x² + a₁x + a₀ = 0`

```typescript
export class Quadratic extends PolyBase {
    constructor(coefs: number[]) {
        super();
        // coefs = [a2, a1, a0] với a2 ≠ 0
        if (coefs.length !== 3 || coefs[0] === 0) {
            throw new Error('Hệ số không hợp lệ cho phương trình bậc hai');
        }
        this.cf = coefs.reverse().map(c => c / coefs[0]);
        this.all_roots = this.__solve();
    }
    
    private __solve(): Complex[] {
        const a1 = this.cf[1], a0 = this.cf[0];
        const discriminant = a1 * a1 - 4 * a0;
        const sqrtDisc = Math.sqrt(Math.abs(discriminant));
        
        if (discriminant >= 0) {
            // Nghiệm thực
            return [
                new Complex((-a1 + sqrtDisc) / 2, 0),
                new Complex((-a1 - sqrtDisc) / 2, 0)
            ];
        } else {
            // Nghiệm phức
            return [
                new Complex(-a1 / 2, sqrtDisc / 2),
                new Complex(-a1 / 2, -sqrtDisc / 2)
            ];
        }
    }
}

// Sử dụng
const quadratic = new Quadratic([1, -5, 6]);  // x² - 5x + 6 = 0
const roots = quadratic.roots();               // [2 + 0i, 3 + 0i]
```

### Phương Trình Bậc Ba

Giải phương trình dạng: `a₃x³ + a₂x² + a₁x + a₀ = 0`

```typescript
class Cubic extends PolyBase {
    private d: number[];
    private SHIFT: Complex;
    
    constructor(coefs: number[]) {
        super();
        // coefs = [a3, a2, a1, a0] với a3 ≠ 0
        if (coefs.length !== 4 || coefs[0] === 0) {
            throw new Error('Hệ số không hợp lệ cho phương trình bậc ba');
        }
        
        const a3 = coefs[0];
        this.cf = coefs.reverse().map(c => c / a3);
        this.all_roots = this.__solve();
    }
    
    private __solve(): Complex[] {
        this._calcDepressedCubic();
        const roots: Complex[] = [];
        const S = this.SHIFT;
        
        if (this.d[1] === 0) {
            // Trường hợp đơn giản: x³ + d₀ = 0
            roots.push(new Complex(Math.cbrt(-this.d[0]), 0).sub(S));
        } else {
            // Trường hợp tổng quát sử dụng công thức Cardano
            const R = new Complex(-this.d[0] / 2, 0);
            const Q = new Complex(this.d[1] / 3, 0);
            const W3 = R.sub(R.squared().add(Q.cubed()).sqrt());
            const Wroots = W3.roots(3);
            
            for (const root of Wroots) {
                roots.push(root.sub(Q.mult(root.pow(-1))).sub(S));
            }
        }
        
        return roots;
    }
}
```

### Phương Trình Bậc Bốn

Giải phương trình dạng: `a₄x⁴ + a₃x³ + a₂x² + a₁x + a₀ = 0`

```typescript
export class Quartic extends PolyBase {
    private d: number[];
    private SHIFT: Complex;
    
    constructor(coefs: number[]) {
        super();
        // coefs = [a4, a3, a2, a1, a0] với a4 ≠ 0
        if (coefs.length !== 5 || coefs[0] === 0) {
            throw new Error('Hệ số không hợp lệ cho phương trình bậc bốn');
        }
        
        const a4 = coefs[0];
        this.cf = coefs.reverse().map(c => c / a4);
        this.all_roots = this.__solve();
    }
    
    private __solve(): Complex[] {
        this.__calcDepressedQuartic();
        
        if (this.d[1] === 0) {
            // Trường hợp biquadratic: x⁴ + d₂x² + d₀ = 0
            return this.__solveBiquadratic();
        } else {
            // Trường hợp tổng quát sử dụng phương pháp Ferrari
            return this.__solveGeneral();
        }
    }
}
```

## Hệ Thống Hệ Số

### Hệ Số Ellipse

Lớp `Coefficients` xử lý thao tác phương trình ellipse:

```typescript
export class Coefficients {
    a: number; b: number; c: number;
    d: number; e: number; f: number;
    
    constructor(a: number, b: number, c: number, d: number, e: number, f: number) {
        // Biểu diễn: ax² + bxy + cy² + dx + ey + f = 0
        this.a = a; this.b = b; this.c = c;
        this.d = d; this.e = e; this.f = f;
    }
    
    // Phép toán số học
    add(other: Coefficients): Coefficients;
    subtract(other: Coefficients): Coefficients;
    mult(scalar: number): Coefficients;
    divide(scalar: number): Coefficients;
    
    // Thao tác hệ số
    getCoef(name: string): number;
    copy(): Coefficients;
}
```

### Phép Toán Hệ Số

```typescript
const ellipse1 = new Coefficients(1, 0, 1, 0, 0, -1);  // x² + y² - 1 = 0 (đường tròn đơn vị)
const ellipse2 = new Coefficients(4, 0, 1, 0, 0, -4);  // 4x² + y² - 4 = 0

// Phép toán số học
const sum = ellipse1.add(ellipse2);
const scaled = ellipse1.mult(2);
const normalized = ellipse1.divide(ellipse1.getCoef('f'));

// Loại bỏ hạng tử
const eliminated = Coefficients.elliminateTerm(ellipse1, ellipse2, 'a');
```

## Sử Dụng Trong Hình Học

### Tính Toán Giao Điểm

```typescript
// Tìm giao điểm của hai đường tròn
function findCircleIntersections(
    circle1: { cx: number, cy: number, r: number },
    circle2: { cx: number, cy: number, r: number }
): Point[] {
    // Thiết lập hệ phương trình
    const coefs = [
        1, 0, 1, -2 * circle1.cx, -2 * circle1.cy, 
        circle1.cx * circle1.cx + circle1.cy * circle1.cy - circle1.r * circle1.r
    ];
    
    // Giải hệ bậc hai
    const poly = PolyBase.getPoly(coefs);
    const roots = poly.roots();
    
    // Lọc nghiệm thực
    return Complex.filterRealRoots(roots).map(root => ({
        x: root.real,
        y: calculateY(root.real, circle1, circle2)
    }));
}
```

### Khớp Đường Cong

```typescript
// Khớp ellipse với các điểm
function fitEllipseToPoints(points: Point[]): Coefficients {
    // Thiết lập hệ bình phương tối thiểu
    const matrix = points.map(p => [
        p.x * p.x, p.x * p.y, p.y * p.y, p.x, p.y, 1
    ]);
    
    // Giải cho hệ số
    const solution = solveLeastSquares(matrix);
    
    return new Coefficients(
        solution[0], solution[1], solution[2],
        solution[3], solution[4], solution[5]
    );
}
```

### Tìm Nghiệm

```typescript
// Tìm nghiệm của đa thức được định nghĩa bởi ràng buộc hình học
function solveGeometricConstraint(constraints: GeometricConstraint[]): Point[] {
    // Chuyển đổi ràng buộc thành hệ số đa thức
    const coefficients = constraintsToPolynomial(constraints);
    
    // Giải đa thức
    const poly = PolyBase.getPoly(coefficients);
    const roots = poly.roots();
    
    // Lọc và chuyển đổi thành điểm hình học
    const realRoots = Complex.filterRealRoots(roots);
    return realRoots.map(root => ({ x: root.real, y: 0 }));
}
```

## Cân Nhắc Hiệu Suất

### Ổn Định Số

```typescript
// Sử dụng epsilon phù hợp cho so sánh
const EPSILON = 1e-10;

function isNearZero(value: number): boolean {
    return Math.abs(value) < EPSILON;
}

function areEqual(a: number, b: number): boolean {
    return Math.abs(a - b) < EPSILON;
}
```

### Chiến Lược Tối Ưu Hóa

1. **Chuẩn Hóa Hệ Số**: Chuẩn hóa hệ số đầu về 1
2. **Làm Mịn Nghiệm**: Tinh chỉnh nghiệm bằng phương pháp Newton
3. **Caching**: Cache các tính toán đắt đỏ
4. **Kết Thúc Sớm**: Dừng khi đạt độ chính xác đủ

### Xử Lý Lỗi

```typescript
try {
    const poly = new Quadratic([0, 1, 2]); // Không hợp lệ: hệ số đầu bằng 0
} catch (error) {
    console.error('Hệ số đa thức không hợp lệ:', error.message);
}

// Kiểm tra vấn đề số
const roots = poly.roots();
const validRoots = roots.filter(root => 
    Number.isFinite(root.real) && Number.isFinite(root.imag)
);
```

## Thực Hành Tốt Nhất

1. **Xác Thực Đầu Vào**: Luôn xác thực hệ số đa thức
2. **Độ Chính Xác Số**: Sử dụng giá trị epsilon phù hợp
3. **Xác Minh Nghiệm**: Xác minh nghiệm bằng thế ngược
4. **Xử Lý Lỗi**: Xử lý các trường hợp suy biến một cách nhẹ nhàng
5. **Hiệu Suất**: Cache các tính toán đắt đỏ
6. **Kiểm Tra**: Kiểm tra với các nghiệm phân tích đã biết

## Ví Dụ Tích Hợp

### Tích Hợp Công Cụ

```typescript
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    private findIntersections(line: RenderLine, circle: RenderCircle): Point[] {
        // Thiết lập phương trình bậc hai cho giao điểm đường-tròn
        const a = line.a, b = line.b, c = line.c;
        const cx = circle.centerX, cy = circle.centerY, r = circle.radius;
        
        // Thế phương trình đường thẳng vào phương trình đường tròn
        const coeffs = this.buildIntersectionCoefficients(a, b, c, cx, cy, r);
        
        // Giải bậc hai
        const quadratic = new Quadratic(coeffs);
        const roots = quadratic.roots();
        
        // Chuyển đổi thành điểm
        return Complex.filterRealRoots(roots).map(root => 
            this.rootToPoint(root, line)
        );
    }
}
```

### Giải Ràng Buộc

```typescript
export class ConstraintSolver {
    solveDistanceConstraint(
        point1: Point, 
        point2: Point, 
        distance: number
    ): Point[] {
        // (x - p1.x)² + (y - p1.y)² = d²
        // (x - p2.x)² + (y - p2.y)² = d²
        
        const coeffs = this.buildDistanceConstraintCoefficients(
            point1, point2, distance
        );
        
        const poly = PolyBase.getPoly(coeffs);
        return this.convertRootsToPoints(poly.roots());
    }
}
