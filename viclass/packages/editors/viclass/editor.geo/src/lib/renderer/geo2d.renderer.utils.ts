import { BaseBoardViewportManager, Position, ViewportManager } from '@viclass/editor.core';
import { <PERSON>derAngle, RenderLine } from '../model';
import { GeoDocCtrl } from '../objects';
import { Geo<PERSON>enderer } from './geo.renderer';

export function isIncrease(a: number, b: number, c: number) {
    return a <= b && b <= c;
}

// Determine the orientation of the triplet (p1, p2, q)
function orientation(p, q, r) {
    const val = (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);
    if (val === 0) return 0; // Collinear
    return val > 0 ? 1 : 2; // Clock or counterclockwise
}

// Check if point q lies on line segment pr
function onSegment(p, q, r) {
    return (
        q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y)
    );
}

/**
 * MARK: does line intersect line
 * Checks if two line segments intersect.
 *
 * @param {number[]} p1 - The first point of the first line segment.
 * @param {number[]} p2 - The second point of the first line segment.
 * @param {number[]} q1 - The first point of the second line segment.
 * @param {number[]} q2 - The second point of the second line segment.
 * @return {boolean} Returns true if the line segments intersect, false otherwise.
 */
export function doesLineIntersectLine(p1: Position, p2: Position, q1: Position, q2: Position) {
    const o1 = orientation(p1, p2, q1);
    const o2 = orientation(p1, p2, q2);
    const o3 = orientation(q1, q2, p1);
    const o4 = orientation(q1, q2, p2);

    if (o1 !== o2 && o3 !== o4) return true;

    if (o1 === 0 && onSegment(p1, q1, p2)) return true;
    if (o2 === 0 && onSegment(p1, q2, p2)) return true;
    if (o3 === 0 && onSegment(q1, p1, q2)) return true;
    if (o4 === 0 && onSegment(q1, p2, q2)) return true;

    return false;
}

/**
 * MARK: does line intersect rectangle
 * Checks if a line segment intersects a rectangle.
 *
 * @param {number} c1 - The coordinates of the starting point of the line segment.
 * @param {number} c2 - The coordinates of the ending point of the line segment.
 * @param {number} r1 - The coordinates of the top-left corner of the rectangle.
 * @param {number} r2 - The coordinates of the bottom-right corner of the rectangle.
 * @return {boolean} Returns true if the line segment intersects the rectangle, otherwise returns false.
 */
export function doesLineIntersectRectangle(c1: Position, c2: Position, r1: Position, r2: Position) {
    // Rectangle corners
    const topLeft = r1;
    const topRight = { x: r2.x, y: r1.y };
    const bottomLeft = { x: r1.x, y: r2.y };
    const bottomRight = r2;

    // Check if the line intersects any of the rectangle's sides
    if (
        doesLineIntersectLine(c1, c2, topLeft, topRight) ||
        doesLineIntersectLine(c1, c2, topLeft, bottomLeft) ||
        doesLineIntersectLine(c1, c2, bottomLeft, bottomRight) ||
        doesLineIntersectLine(c1, c2, topRight, bottomRight)
    ) {
        return true;
    }

    return false;
}

/**
 * MARK: does line intersect ellipse
 * Checks if a line segment intersects an ellipse segment.
 *
 * @param {number} c1 - The coordinates of the starting point of the line segment.
 * @param {number} c2 - The coordinates of the ending point of the line segment.
 * @param {number} e - The coordinates of the center of the ellipse.
 * @param {number} a - The horizontal radius of the ellipse.
 * @param {number} b - The vertical radius of the ellipse.
 * @param {number} ellipseAngle - The angle of rotation of the ellipse in radians.
 * @param {number} startAngle - The starting angle of the ellipse segment in radians.
 * @param {number} endAngle - The ending angle of the ellipse segment in radians.
 * @param {boolean} isClockwise - Indicates whether the ellipse segment is in clockwise or counter-clockwise direction.
 * @return {boolean} Returns true if the line segment intersects the ellipse segment, otherwise returns false.
 */
export function doesLineIntersectEllipseSegment(
    c1: Position,
    c2: Position,
    e: Position,
    a: number,
    b: number,
    ellipseAngle: number, // radian
    startAngle: number, // radian
    endAngle: number, // radian
    isClockwise: boolean // true nếu cung ellipse theo chiều kim đồng hồ, false nếu ngược chiều
) {
    // Biến đổi tọa độ
    const cosAngle = Math.cos(-ellipseAngle);
    const sinAngle = Math.sin(-ellipseAngle);

    const dx1 = c1.x - e.x;
    const dy1 = c1.y - e.y;
    const dx2 = c2.x - e.x;
    const dy2 = c2.y - e.y;

    const x1Rot = dx1 * cosAngle - dy1 * sinAngle;
    const y1Rot = dx1 * sinAngle + dy1 * cosAngle;
    const x2Rot = dx2 * cosAngle - dy2 * sinAngle;
    const y2Rot = dx2 * sinAngle + dy2 * cosAngle;

    // Tính toán giao điểm
    const A = (x2Rot - x1Rot) ** 2 / (a * a) + (y2Rot - y1Rot) ** 2 / (b * b);
    const B = (2 * x1Rot * (x2Rot - x1Rot)) / (a * a) + (2 * y1Rot * (y2Rot - y1Rot)) / (b * b);
    const C = x1Rot ** 2 / (a * a) + y1Rot ** 2 / (b * b) - 1;

    const discriminant = B * B - 4 * A * C;

    if (discriminant < 0) return false;

    const t1 = (-B + Math.sqrt(discriminant)) / (2 * A);
    const t2 = (-B - Math.sqrt(discriminant)) / (2 * A);

    // Kiểm tra xem giao điểm có nằm trong đoạn thẳng và cung ellipse
    if (!(0 <= t1 && t1 <= 1) && !(0 <= t2 && t2 <= 1)) return false;

    for (const t of [t1, t2]) {
        if (0 <= t && t <= 1) {
            const xIntersect = x1Rot + t * (x2Rot - x1Rot);
            const yIntersect = y1Rot + t * (y2Rot - y1Rot);

            let angle = Math.atan2(yIntersect, xIntersect);
            if (angle < 0) angle += 2 * Math.PI;

            if (isClockwise)
                if (startAngle <= angle && angle <= endAngle) return true;
                else if (endAngle <= angle && angle <= startAngle) return true;
        }
    }

    return false;
}

/**
 * MARK: does rectangle intersect ellipse segment
 * Checks if a rectangle intersects an ellipse segment.
 *
 * @param {number} c1 - The coordinates of the top-left corner of the rectangle.
 * @param {number} c2 - The coordinates of the bottom-right corner of the rectangle.
 * @param {number} e - The coordinates of the center of the ellipse.
 * @param {number} a - The horizontal radius of the ellipse.
 * @param {number} b - The vertical radius of the ellipse.
 * @param {number} ellipseAngle - The angle of rotation of the ellipse in radians.
 * @param {number} startAngle - The starting angle of the ellipse segment in radians.
 * @param {number} endAngle - The ending angle of the ellipse segment in radians.
 * @param {boolean} isClockwise - Indicates whether the ellipse segment is in clockwise or counter-clockwise direction.
 * @return {boolean} Returns true if the rectangle intersects the ellipse segment, otherwise returns false.
 */
export function doesRectangleIntersectEllipseSegment(
    c1: Position,
    c2: Position,
    e: Position,
    a: number,
    b: number,
    ellipseAngle: number, // radian
    startAngle: number, // radian
    endAngle: number, // radian
    isClockwise: boolean
) {
    const checkLines = [
        [c1.x, c1.y, c2.x, c1.y],
        [c1.x, c1.y, c1.x, c2.y],
        [c1.x, c2.y, c2.x, c2.y],
        [c2.x, c1.y, c2.x, c2.y],
    ];
    for (const line of checkLines) {
        const [x1, y1, x2, y2] = line;
        if (
            doesLineIntersectEllipseSegment(
                { x: x1, y: y1 },
                { x: x2, y: y2 },
                e,
                a,
                b,
                ellipseAngle,
                startAngle,
                endAngle,
                isClockwise
            )
        )
            return true;
    }

    return false;
}

/**
 * MARK: calculate bottom left by angle xy size
 * Calculates the bottom-left coordinates of a label based on its angle, x and y coordinates,
 * label width, and label height.
 *
 * @param {number} angle - The angle of the label in degrees.
 * @param {number} l - The coordinates of the label.
 * @param {number} labelWidth - The width of the label.
 * @param {number} labelHeight - The height of the label.
 * @return {Object|null} An object with the x and y coordinates of the bottom-left point of the label,
 * or null if the angle is not within the range of 0 to 360.
 */
export function calculateBottomLeftByAngleXYSize(
    angle: number,
    l: Position,
    labelWidth: number,
    labelHeight: number,
    padding: number = 0
) {
    const rad = angle * (Math.PI / 180);

    // các trường hợp right, bottom, left, top
    if (360 - 3 < angle || angle < 3) {
        return { x: l.x + padding - labelWidth / 2, y: l.y - labelHeight / 2 }; // x, y will be center-left of the label
    } else if (90 - 3 < angle && angle < 90 + 3) {
        return { x: l.x - padding - labelWidth / 2, y: l.y - padding }; // x, y will be bottom-center of the label
    } else if (180 - 3 < angle && angle < 180 + 3) {
        return { x: l.x - padding - labelWidth / 2, y: l.y - labelHeight / 2 }; // x, y will be center-right of the label
    } else if (270 - 3 < angle && angle < 270 + 3) {
        return { x: l.x - labelWidth / 2, y: l.y - padding - labelHeight / 2 }; // x, y will be top-center of the label
    }

    // if (angle > 0 && angle < 90) return l; // x, y will be top-left of the label
    // if (angle > 90 && angle < 180)
    //     return {
    //         x: l.x + padding * Math.cos(rad) - labelWidth / 2,
    //         y: l.y + padding * Math.sin(rad) - labelHeight / 2,
    //     }; // x, y will be top-right of the label
    // if (angle > 180 && angle < 270)
    //     return {
    //         x: l.x + padding * Math.sin(rad) - labelWidth / 2,
    //         y: l.y + padding * Math.cos(rad) - labelHeight / 2,
    //     }; // x, y will be bottom-right of the label
    // if (angle > 270 && angle < 360) return { x: l.x, y: l.y - padding - labelHeight / 2 }; // x, y will be bottom-left of the label

    // return { x: l.x - padding - labelWidth / 2, y: l.y + padding + labelHeight / 2 }; // default top-left

    return {
        x: l.x + padding * Math.cos(rad) - labelWidth / 2,
        y: l.y + padding * Math.sin(rad) - labelHeight / 2,
    };
}

/**
 * MARK: get xy from angle and vertex
 * Calculates the x and y coordinates based on the given angle, vertex coordinates, and radius.
 *
 * @param {number} angle - The angle in degrees.
 * @param {number} c - The coordinates of the vertex.
 * @param {number} radius - The radius.
 * @return {Object} An object containing the calculated x and y coordinates and the angle.
 */
export function getXYFromAngleAndVertex(angle: number, v: Position, radius: number) {
    angle %= 360;
    const radian = angle * (Math.PI / 180); // Chuyển đổi độ sang radian
    const pointX = v.x + radius * Math.cos(radian);
    const pointY = v.y + radius * Math.sin(radian);
    return { x: pointX, y: pointY, angle };
}

/**
 * MARK: get label center points from vertex
 * Generates the center points of labels for a given vertex.
 *
 * @param {number} v - The coordinates of the vertex.
 * @param {number} radius - The radius of the vertex.
 * @param {number} initialAngle - The initial angle of the vertex (radian).
 * @yields {Object} - The center point and angle of each label.
 */
export function* getLabelCenterPointsFromVertex(
    v: Position,
    radius: number,
    initialAngle: number // radian
) {
    yield getXYFromAngleAndVertex(initialAngle, v, radius);
    const angleIncrement = 22.5; // Góc giữa các điểm
    const step = Math.floor(360 / angleIncrement);
    let angle = initialAngle != null ? initialAngle : 225; // Góc của điểm đầu tiên
    for (let i = 0; i < step; i++) {
        angle %= 360;
        yield getXYFromAngleAndVertex(angle, v, radius);
        angle += angleIncrement;
    }
}

export function getLabelMeasure(ctx: CanvasRenderingContext2D, content: string, fontSize: number): TextMetrics {
    ctx.save();
    ctx.beginPath();

    ctx.font = fontSize + 'px Montserrat';

    const labelMetrics = ctx.measureText(content);

    ctx.closePath();
    ctx.restore();

    return labelMetrics;
}

export function drawLabel(
    ctx: CanvasRenderingContext2D,
    content: string,
    bottomLeft: Position,
    options?: {
        fontSize?: number;
        textColor?: string;
        radAngleFromXAxis?: number; // 0 - 360
        flipVertical?: boolean;
        flipHorizontal?: boolean;
        padding?: {
            top?: number;
            left?: number;
        };
    }
) {
    const fontSize = options?.fontSize ?? 16;
    const textColor = options?.textColor ?? 'black';
    const radAngleFromXAxis = options?.radAngleFromXAxis ?? 0;
    const paddingTop = options?.padding?.top ?? 0;
    const paddingLeft = options?.padding?.left ?? 0;
    const flipVertical = options?.flipVertical ?? false;
    const flipHorizontal = options?.flipHorizontal ?? false;
    const labelMeasure = getLabelMeasure(ctx, content, fontSize);

    ctx.save();
    ctx.beginPath();

    ctx.font = fontSize + 'px Montserrat';
    ctx.fillStyle = textColor;

    ctx.translate(bottomLeft.x, bottomLeft.y);
    ctx.rotate(radAngleFromXAxis - Math.PI);
    ctx.scale(flipHorizontal ? 1 : -1, flipVertical ? -1 : 1);

    // draw stroke text
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    ctx.strokeText(content, paddingLeft, paddingTop);

    ctx.fillText(content, flipHorizontal ? -(paddingLeft + labelMeasure.width) : paddingLeft, paddingTop);

    ctx.closePath();
    ctx.restore();
}

export function drawLine(
    ctx: CanvasRenderingContext2D,
    x1: number,
    y1: number,
    x2: number,
    y2: number,
    options: {
        color?: string;
        lineWeight?: number;
        lineDash?: number[];
        withoutSave?: boolean;
    }
) {
    const color = options?.color ?? 'black';
    const lineWeight = options?.lineWeight ?? 1;
    const withoutSave = options?.withoutSave ?? false;

    if (!withoutSave) ctx.save(); // Save the current context state
    ctx.beginPath();

    if (options?.lineDash?.length) ctx.setLineDash(options.lineDash);
    ctx.lineWidth = lineWeight;
    ctx.strokeStyle = color;

    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();

    ctx.closePath();
    if (!withoutSave) ctx.restore();
}

export function drawArc(
    ctx: CanvasRenderingContext2D,
    startCoords: Position,
    radius: number,
    startAngle: number,
    endAngle: number,
    options: {
        color?: string;
        lineWeight?: number;
        lineDash?: number[];
    }
) {
    const color = options?.color ?? 'black';
    const lineWeight = options?.lineWeight ?? 1;

    ctx.save();
    ctx.beginPath();

    if (options?.lineDash?.length) ctx.setLineDash(options.lineDash);
    ctx.lineWidth = lineWeight;
    ctx.strokeStyle = color;

    ctx.arc(startCoords.x, startCoords.y, radius, startAngle, endAngle, false);
    ctx.stroke();

    ctx.closePath();
    ctx.restore();
}

export function drawParallelLines(
    ctx: CanvasRenderingContext2D,
    start: Position,
    end: Position,
    padding: number = 6, // Define Distance for Parallel Lines (in pixels). Only for `rep` > 1
    rep: number = 1, // Replica
    options?: {
        color?: string;
        lineWeight?: number;
        lineDash?: number[];
    }
) {
    if (rep < 1 || rep !== Math.floor(rep)) return;

    if (rep === 1) {
        drawLine(ctx, start.x, start.y, end.x, end.y, options);
        return;
    }

    // Calculate Direction Vector
    const dx = start.x - end.x;
    const dy = start.y - end.y;

    // Calculate Perpendicular Vector
    const perpVectorX = -dy;
    const perpVectorY = dx;

    // Normalize Perpendicular Vector
    const magnitude = Math.sqrt(perpVectorX * perpVectorX + perpVectorY * perpVectorY);
    const normalizedPerpX = perpVectorX / magnitude;
    const normalizedPerpY = perpVectorY / magnitude;

    const initPerCoords =
        rep % 2 !== 0
            ? [start, end]
            : [
                  {
                      x: start.x - normalizedPerpX * (padding / 2),
                      y: start.y - normalizedPerpY * (padding / 2),
                  },
                  {
                      x: end.x - normalizedPerpX * (padding / 2),
                      y: end.y - normalizedPerpY * (padding / 2),
                  },
              ];

    const curPerCoords = [
        {
            x: initPerCoords[0].x,
            y: initPerCoords[0].y,
        },
        {
            x: initPerCoords[1].x,
            y: initPerCoords[1].y,
        },
    ]; // Clone initPerCoords
    let jumpSide: 'left' | 'right' = 'right';

    for (let i = 0; i < rep; i++) {
        curPerCoords[0].x += normalizedPerpX * (padding * i) * (jumpSide === 'right' ? -1 : 1);
        curPerCoords[0].y += normalizedPerpY * (padding * i) * (jumpSide === 'right' ? -1 : 1);
        curPerCoords[1].x += normalizedPerpX * (padding * i) * (jumpSide === 'right' ? -1 : 1);
        curPerCoords[1].y += normalizedPerpY * (padding * i) * (jumpSide === 'right' ? -1 : 1);

        drawLine(ctx, curPerCoords[0].x, curPerCoords[0].y, curPerCoords[1].x, curPerCoords[1].y, options);

        jumpSide = jumpSide === 'right' ? 'left' : 'right'; // Switch side
    }
}

export function getAngleWithOx(c1: Position, c2: Position) {
    const deltaX = c2.x - c1.x;
    const deltaY = c2.y - c1.y;

    let angleInRadians = Math.atan2(deltaY, deltaX);

    // Chuyển góc về khoảng từ 0 đến 2π (nếu cần)
    angleInRadians = angleInRadians >= 0 ? angleInRadians : angleInRadians + 2 * Math.PI;

    // chuyển góc về độ
    return (angleInRadians * 180) / Math.PI;
}

export function calculateZoomLevel(docCtrl: GeoDocCtrl, viewport: ViewportManager) {
    return ((viewport as BaseBoardViewportManager)?.zoomLevel ?? 1) / (docCtrl.state.docRenderProp?.scale ?? 1);
}

export function calculateSpacing(docCtrl: GeoDocCtrl, viewport: ViewportManager) {
    const noThinerLine = 5;
    const zoomLevel = calculateZoomLevel(docCtrl, viewport);

    // Adjust grid spacing based on zoom level
    let spacing = noThinerLine;
    const ratioToRescaleSpacing = 1.25;
    while (spacing / zoomLevel / noThinerLine >= ratioToRescaleSpacing) spacing /= 2;
    while (noThinerLine / (spacing / zoomLevel) >= ratioToRescaleSpacing) spacing *= 2;

    if (spacing <= 0.1) spacing = 0.1;
    else if (spacing <= 0.2) spacing = 0.2;
    else if (spacing <= 0.5) spacing = 0.5;
    else spacing = Math.round(spacing);

    return spacing;
}

export function calculateAngle(renderer: GeoRenderer, rel: RenderAngle) {
    const startLine = rel.vector('start', renderer);
    const endLine = rel.vector('end', renderer);

    if (!startLine || !endLine) throw new Error('Start or end line not found');

    const vS = startLine;
    const vE = endLine;
    const startAngleInRad = Math.atan2(vS[1], vS[0]);
    const endAngleInRad = (() => {
        let angle = Math.atan2(vE[1], vE[0]);
        while (angle > startAngleInRad) angle -= 2 * Math.PI;
        return angle;
    })(); // normalize by startAngleInRad

    let angle = Math.round(Math.abs(((Math.abs(startAngleInRad - endAngleInRad) - 2 * Math.PI) / Math.PI) * 180));
    angle = ((angle % 360) + 360) % 360; // normalize by 360 degree
    return angle;
}
