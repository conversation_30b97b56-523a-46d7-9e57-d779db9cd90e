import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy } from '@angular/core';
import { ToolBar, ToolEventData, ToolEventListener, ToolState } from '@viclass/editor.core';
import { GeometryTool, GeometryToolBar, GeometryToolType, EllipseToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';

/**
 * Component for display and edit the drawing mode for CreateEllipseTool
 */
@Component({
    selector: 'tb-ellipse-mode',
    templateUrl: './ellipse-mode.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EllipseModeComponent implements AfterViewInit, OnDestroy {
    private toolListener = EllipseModeComponent.ToolListener(this);

    public mode$ = new BehaviorSubject<number>(0);

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {}

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    setMode(value: number) {
        const ts = this.toolbar.toolState(this.tooltype) as EllipseToolState;
        ts.mode = value;
        this.toolbar.update(this.tooltype, ts);
    }

    get mode(): number {
        const ts = this.toolbar.toolState(this.tooltype) as EllipseToolState;
        return ts.mode;
    }

    private updateInputFromToolState() {
        this.mode$.next(this.mode);
    }

    private static ToolListener(
        _p: EllipseModeComponent
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                if (eventData.toolType != _p.tooltype) return eventData;
                _p.updateInputFromToolState();
                _p.changeDetectorRef.markForCheck();

                return eventData;
            }
        })();
    }
}
