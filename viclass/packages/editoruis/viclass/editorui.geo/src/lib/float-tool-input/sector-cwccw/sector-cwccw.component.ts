import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy } from '@angular/core';
import { ToolBar, ToolEventData, ToolEventListener, ToolState } from '@viclass/editor.core';
import { GeometryTool, GeometryToolBar, GeometryToolType, SectorToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';

/**
 * Component for display and edit the clockwise/counterclockwise mode for CreateSectorTool
 */
@Component({
    selector: 'tb-sector-cwccw',
    templateUrl: './sector-cwccw.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SectorCWCCWComponent implements AfterViewInit, OnDestroy {
    private toolListener = SectorCWCCWComponent.ToolListener(this);

    public cwccw$ = new BehaviorSubject<boolean>(true);

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {}

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    setCWCCW(value: boolean) {
        const ts = this.toolbar.toolState(this.tooltype) as SectorToolState;
        ts.clockwise = value;
        this.toolbar.update(this.tooltype, ts);
    }

    get cwccw(): boolean {
        const ts = this.toolbar.toolState(this.tooltype) as SectorToolState;
        return ts.clockwise;
    }

    private updateInputFromToolState() {
        this.cwccw$.next(this.cwccw);
    }

    private static ToolListener(
        _p: SectorCWCCWComponent
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                if (eventData.toolType != _p.tooltype) return eventData;
                _p.updateInputFromToolState();
                _p.changeDetectorRef.markForCheck();

                return eventData;
            }
        })();
    }
}
