<div class="regpolygon-edge-input-overlay shadow-SH1 gap-1">
    <div class="flex justify-center items-center gap-[20px]">
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': (mode$ | async) === 0 }"
            (click)="setMode(0)">
            Points
        </button>
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': (mode$ | async) === 1 }"
            (click)="setMode(1)">
            Center-Vectors
        </button>
    </div>
</div>
