<div class="regpolygon-edge-input-overlay shadow-SH1 gap-1">
    <div class="flex justify-center items-center gap-[20px]">
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': cwccw$ | async }"
            (click)="setCWCCW(true)">
            Clockwise
        </button>
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': !(cwccw$ | async) }"
            (click)="setCWCCW(false)">
            Counter CW
        </button>
    </div>
</div>
