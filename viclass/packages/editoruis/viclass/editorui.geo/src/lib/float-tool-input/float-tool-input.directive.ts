import {
    ConnectedPosition,
    ConnectionPositionPair,
    HorizontalConnectionPos,
    Overlay,
    OverlayConfig,
    OverlayRef,
    VerticalConnectionPos,
} from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { AfterViewInit, Directive, Injector, Input } from '@angular/core';
import { ToolBar, ToolEventData, ToolEventListener, ToolState } from '@viclass/editor.core';
import { GeometryTool, GeometryToolBar, GeometryToolType } from '@viclass/editor.geo';
import { TOOLBAR, TOOLTYPE } from './injection.token';
import { EllipseModeComponent } from './ellipse-mode/ellipse-mode.component';
import { Quad2SideSoDComponent } from './quad-2side-sod/quad-2side-sod.component';
import { QuadSoDComponent } from './quadrilateral-sod/quadrilateral-sod.component';
import { RegularPolygonEdgesInputComponent } from './regular-polygon-edges-input/regular-polygon-edges-input.component';
import { SectorCWCCWComponent } from './sector-cwccw/sector-cwccw.component';
import { TriangleSOBComponent } from './triangle-sob/triangle-sob.component';

/**
 * This directive attach a float UI of corresponding component to the viewport root at set position using overlay.
 */
@Directive({
    selector: '[tb-float-tool-input]',
    exportAs: 'tbFloatToolInput',
})
export class FloatToolInput implements AfterViewInit {
    @Input()
    toolBar: GeometryToolBar;

    @Input()
    floatVA: 'top' | 'center' | 'bottom';

    @Input()
    floatHA: 'left' | 'center' | 'right';

    @Input()
    direction: 'ltr' | 'rtl' | 'btt' | 'ttb';

    private overlayRef?: OverlayRef;
    private toolListener = FloatToolInput.ToolListener(this);

    private curFocus: GeometryToolType | undefined;

    private toolTypeToComponent: Partial<{ [key in GeometryToolType]: any }> = {
        CreateRightTriangleTool: TriangleSOBComponent,
        CreateIsoscelesTriangleTool: TriangleSOBComponent,
        CreateIsoscelesRightTriangleTool: TriangleSOBComponent,
        CreateSectorTool: SectorCWCCWComponent,
        CreateEllipseTool: EllipseModeComponent,
        CreateRegularPolygonTool: RegularPolygonEdgesInputComponent,
        CreateRectangleTool: QuadSoDComponent,
        CreateParallelogramTool: Quad2SideSoDComponent,
        CreateSquareTool: QuadSoDComponent,
        CreateRhombusTool: Quad2SideSoDComponent,
        CreateTrapezoidTool: QuadSoDComponent,
    };

    constructor(
        private overlay: Overlay,
        private injector: Injector
    ) {}

    /**
     * Overlay positions for the overlay containing the input element.
     * These positions are calculated based on the input direction and vAlign.
     */
    get overlayPositions(): ConnectedPosition[] {
        const horizontalConnectionPos: HorizontalConnectionPos = 'center';
        let verticalConnectionPos: VerticalConnectionPos = 'top';

        const offsetX: number = 0;
        let offsetY: number = 25;

        const isHorizontal = this.direction == 'ltr' || this.direction == 'rtl';
        if (isHorizontal && this.floatVA == 'top') {
            verticalConnectionPos = 'bottom';
            offsetY = -25;
        }

        return [
            new ConnectionPositionPair(
                {
                    originX: horizontalConnectionPos,
                    originY: verticalConnectionPos,
                },
                {
                    overlayX: horizontalConnectionPos,
                    overlayY: verticalConnectionPos,
                },
                offsetX,
                offsetY
            ),
        ];
    }

    /**
     * Setup the component.
     * 1. Subscribe to the pointClickNumber$ event of the CreateRegularPolygonTool
     *    and attach/detach the input element based on the click number.
     * 2. Register the {@link RegularPolygonEdgesInputComponent.toolListener} to the toolBar.
     * 3. Set the initial value of the input element to the number of edges of the tool.
     */
    ngAfterViewInit(): void {
        this.toolBar.registerToolListener(this.toolListener);
    }

    /**
     * Clean up resources and detach the regular polygon input on component destruction.
     */
    ngOnDestroy(): void {
        this.detachFloatUI();
        this.toolBar.unregisterToolListener(this.toolListener);
    }

    /**
     * Attach the regular polygon edges input to the viewport.
     * This is only done when the tool is in the first or second click state.
     * The input element is attached to the viewport and positioned at the top of the viewport.
     */
    attachFloatUI(toolType: keyof typeof this.toolTypeToComponent | undefined) {
        if (!toolType || !(toolType in this.toolTypeToComponent)) {
            this.detachFloatUI();
            return;
        }

        if (toolType != this.curFocus) {
            this.detachFloatUI();
        }

        if (this.curFocus == toolType) return;

        this.curFocus = toolType;

        const vpRoot = this.toolBar?.viewport?.rootEl;
        if (!vpRoot) return;

        const config = new OverlayConfig({
            positionStrategy: this.overlay.position().flexibleConnectedTo(vpRoot).withPositions(this.overlayPositions),
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
        });
        this.overlayRef = this.overlay.create(config);

        const compRef = this.overlayRef.attach(
            new ComponentPortal(
                this.toolTypeToComponent[toolType],
                null,
                Injector.create({
                    providers: [
                        { provide: TOOLBAR, useValue: this.toolBar },
                        { provide: TOOLTYPE, useValue: toolType },
                    ],
                    parent: this.injector,
                })
            )
        );
    }

    /**
     * Detaches the regular polygon input overlay from the viewport if it is currently attached.
     */
    detachFloatUI() {
        this.curFocus = undefined;
        this.overlayRef?.detach();
        this.overlayRef = undefined;
    }

    /**
     * The tool listener for the regular polygon edges input component.
     * It updates the input value based on the tool state when the tool type is 'CreateRegularPolygonTool'.
     */
    private static ToolListener(
        _p: FloatToolInput
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                switch (eventData.eventType) {
                    case 'focus':
                        _p.attachFloatUI(eventData.toolType);
                        break;

                    case 'blur':
                        _p.detachFloatUI();
                        break;
                }

                return eventData;
            }
        })();
    }
}
