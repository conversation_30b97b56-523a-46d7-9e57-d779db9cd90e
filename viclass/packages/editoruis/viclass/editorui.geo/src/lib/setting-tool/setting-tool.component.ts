/**
 * SettingToolComponent is a component that provides a UI interface for configuring
 * various properties of geometric elements in the editor. It handles settings for
 * different types of geometric elements like points, lines, shapes, and angles.
 */
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    HostListener,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core';
import { ChangeToolEventData, ToolEventListener } from '@viclass/editor.core';
import {
    DefaultGeoRenderProp,
    DocRenderPropType,
    GeometryToolBar,
    GeometryToolType,
    GeoRenderElement,
    GeoToolEventData,
    isValidIdx,
    RenameElementTool,
    RenderAngle,
    settingPropertyConfig,
    SettingPropertyGroup,
    SettingPropertyType,
    UpdatePropTool,
    UpdatePropToolState,
    validateName,
} from '@viclass/editor.geo';
import { SettingFieldChangeEmitterData, SettingFieldValue } from './setting-tool.models';

@Component({
    selector: 'setting-tool',
    templateUrl: './setting-tool.component.html',
    styleUrls: ['./setting-tool.component.sass'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolComponent implements OnInit, OnDestroy {
    DocRenderPropType = DocRenderPropType;
    SettingPropertyType = SettingPropertyType;

    @Input() tb!: GeometryToolBar;

    /** Event emitter for when the tool is closed */
    @Output() onClose: EventEmitter<boolean>;

    /** Reference to the element name input field */
    @ViewChild('elementNameInput') elementNameInput: ElementRef<HTMLInputElement>;

    /** Reference to the label input field */
    @ViewChild('labelInput') labelInput: ElementRef<HTMLInputElement>;

    /** Error message for point label validation */
    pointLabelError: string | null = null;

    /** Reference to the point label input field */
    @ViewChild('pointLabelInput') pointLabelInput: ElementRef<HTMLInputElement>;

    /** Error message for point name validation */
    pointNameError: string | null = null;

    /** Currently selected tab in the settings panel */
    selectedTab: 'geo' | 'obj' | 'docDefaultElRenderProps' = 'geo';

    /** Map of color codes to their corresponding CSS classes */
    readonly colorList: Map<string, string> = new Map([
        ['#00AEEF', 'bg-P1'],
        ['#121414', 'bg-BW1'],
        ['#DB00FF', 'bg-SC1'],
        ['#31E37C', 'bg-SC2'],
        ['#FFD600', 'bg-SC3'],
        ['#FF7A00', 'bg-SC4'],
        ['#FF002E', 'bg-SC5'],
    ]);

    /** Map of shadow styles to their corresponding CSS classes */
    readonly shadowList: Map<string, string> = new Map([
        ['sd1', 'shadow-SH1'],
        ['sd2', 'shadow-SH2'],
        ['sd3', 'shadow-SH3'],
    ]);

    /** Map of border styles to their corresponding CSS classes */
    readonly borderList: Map<string, string> = new Map([
        ['bd1', 'bd1'],
        ['bd2', 'bd2'],
        ['bd3', 'bd3'],
    ]);

    /** Map of line stroke styles to their corresponding CSS classes */
    readonly lineStrokeList: Map<string, string> = new Map([
        ['Solid', 'vcon vcon-geometry vcon_line-style-1'],
        ['Dotted', 'vcon vcon-geometry vcon_line-style-2'],
        ['Dashed', 'vcon vcon-geometry vcon_line-style-4'],
        ['DashedDotted', 'vcon vcon-geometry vcon_line-style-3'],
    ]);

    /** Map of angle style types to their corresponding CSS classes */
    readonly angleStyleList: Map<string, string> = new Map([
        ['as1', 'vcon vcon-geometry vcon_angel-style_1'],
        ['as2', 'vcon vcon-geometry vcon_angel-style_2'],
        // ['as3', 'vcon vcon-geometry vcon_angel-style_3'],
        // ['as4', 'vcon vcon-geometry vcon_angel-style_4'],
        // ['as5', 'vcon vcon-geometry vcon_angel-style_5'],
        // ['as6', 'vcon vcon-geometry vcon_angel-style_6'],
        // ['as7', 'vcon vcon-geometry vcon_angel-style_7'],
    ]);

    /** Available opacity values */
    readonly opacityList: number[] = [0, 5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

    /** Map of setting property types to their display labels */
    readonly settingLabel: {
        [key in SettingPropertyType]: string;
    } = {
        [SettingPropertyType.showLabel]: 'Hiển thị nhãn dán / tên',
        [SettingPropertyType.label]: 'Nội dung hiển thị',
        [SettingPropertyType.swapLabelPosition]: 'Đảo vị trí nhãn',
        [SettingPropertyType.showAngleTypes]: 'Kiểu hiển thị',
        [SettingPropertyType.angleArc]: 'Số cung',
        [SettingPropertyType.spaceFromArcToCorner]: 'Khoảng cách cung',
        [SettingPropertyType.strokeStyle]: 'Kiểu hiển thị',
        [SettingPropertyType.lineWeight]: 'Độ dày',
        [SettingPropertyType.color]: 'Màu',
        [SettingPropertyType.lineColor]: 'Màu viền',
        [SettingPropertyType.opacity]: 'Hiển thị nền',
        [SettingPropertyType.enableEqualSegmentSign]: 'Ký hiệu bằng nhau',
        [SettingPropertyType.showArcLabel]: 'Hiển thị nhãn dáng / tên cung tròn',
        [SettingPropertyType.arcLabelType]: 'Kiểu hiện thị',
        [SettingPropertyType.arcLabelContent]: 'Nội dung hiện thị',
        [SettingPropertyType.isShowAngleSize]: 'Hiển thị số đo góc',
        [SettingPropertyType.hidden]: '',
        [SettingPropertyType.labelType]: '',
        [SettingPropertyType.equalSegmentSign]: '',
        [SettingPropertyType.pointColor]: 'Màu điểm',
        [SettingPropertyType.pointLabelType]: 'Kiểu nhãn dán',
        [SettingPropertyType.pointLabelFreeContent]: 'Nội dung hiển thị',
        [SettingPropertyType.showPointLabel]: 'Hiển thị nhãn dán điểm',
    };

    /** Map of property groups to their display labels */
    readonly geoRenderPropModelLabel: {
        [key in SettingPropertyGroup | 'Others']: string;
    } = {
        Default: 'Mặc định',
        Line: 'Viền',
        Point: 'Điểm',
        Shape: 'Hình',
        Angle: 'Góc',
        Others: 'Khác',
    };

    /** Map storing properties grouped by their property group */
    properties: Map<SettingPropertyGroup | 'Others', Map<SettingPropertyType, SettingFieldValue>> = new Map();

    /**
     * Gets the list of property groups with their associated properties
     * @returns Array of tuples containing group name and its properties
     */
    get propertiesGroupList() {
        const result: Array<[SettingPropertyGroup | 'Others', Array<[SettingPropertyType, SettingFieldValue]>]> = [];

        for (const [key] of this.properties.entries())
            if (key !== 'Others') result.push([key, Array.from(this.properties.get(key))]);
        if (this.properties.get('Others')) {
            const othersItems = Array.from(this.properties.get('Others'))
                // Temporarily disable hidden property
                .filter(p => p[0] !== SettingPropertyType.hidden);
            if (othersItems.length) result.push(['Others', othersItems]);
        }

        return result;
    }

    /**
     * Track function for property groups in ngFor
     * @param _ Index
     * @param item Property group item
     * @returns Group name as a track key
     */
    trackByGroup(_: number, item: [SettingPropertyGroup | 'Others', Array<[SettingPropertyType, SettingFieldValue]>]) {
        return item[0];
    }

    /**
     * Track function for properties within a group in ngFor
     * @param _ Index
     * @param item Property item
     * @returns Property type as a track key
     */
    trackByGroupProps(_: number, item: [SettingPropertyType, SettingFieldValue]) {
        return item[0];
    }

    private readonly updatePropToolEventListener: ToolEventListener<GeometryToolBar, GeometryToolType>;

    constructor(private cdr: ChangeDetectorRef) {
        this.onClose = new EventEmitter();

        this.updatePropToolEventListener = new (class implements ToolEventListener<GeometryToolBar, GeometryToolType> {
            constructor(private c: SettingToolComponent) {}

            get tb() {
                return this.c.tb;
            }

            onEvent(eventData: GeoToolEventData): GeoToolEventData {
                const changeEvent = eventData as ChangeToolEventData<GeometryToolBar, GeometryToolType>;
                if (changeEvent.toolType !== 'UpdatePropTool') return eventData;
                if (this.tb.isDisabled()) {
                    this.c.cdr.markForCheck();
                    return eventData;
                }

                if (eventData.eventType === 'change') {
                    const state = changeEvent.state as UpdatePropToolState;
                    if (changeEvent.changes.has('docRenderProp')) {
                        this.c.applyNewDocRenderPropToState(state);
                        this.c.cdr.markForCheck();
                    }
                    if (changeEvent.changes.has('docDefaultElRenderProps')) {
                        this.c.applyNewDocDefaultElRenderPropsToState(state);
                        this.c.cdr.markForCheck();
                    }
                    if (changeEvent.changes.has('selectedElements')) {
                        this.c.replaceNewSelectedElementsToState(state);
                        this.c.cdr.markForCheck();
                    }
                }

                return eventData;
            }
        })(this);
    }

    /**
     * Checks if label content editing is disabled based on label type settings
     */
    get isLabelContentDisabled() {
        for (const g of this.properties.values())
            if (g.has(SettingPropertyType.labelType))
                if (g.get(SettingPropertyType.labelType).value !== 'free') return true;
        return this.isLabelTypeDisabled;
    }

    /**
     * Checks if arc label content editing is disabled based on arc label type settings
     */
    get isArcLabelContentDisabled() {
        for (const g of this.properties.values())
            if (g.has(SettingPropertyType.arcLabelType))
                if (g.get(SettingPropertyType.arcLabelType).value !== 'free') return true;
        return this.isArcLabelTypeDisabled;
    }

    /**
     * Checks if label type editing is disabled based on show label settings
     */
    get isLabelTypeDisabled() {
        for (const g of this.properties.values())
            if (g.has(SettingPropertyType.showLabel)) return !g.get(SettingPropertyType.showLabel).value;
        return true;
    }

    /**
     * Checks if point label type editing is disabled based on show label settings
     */
    get isPointLabelTypeDisabled() {
        for (const g of this.properties.values())
            if (g.has(SettingPropertyType.showPointLabel)) return !g.get(SettingPropertyType.showPointLabel).value;
        return true;
    }

    /**
     * Checks if point-label-free content editing is disabled based on show label and point label type settings
     */
    get isPointLabelFreeContentDisabled() {
        // First check if showPointLabel is enabled
        let showPointLabelEnabled = false;
        for (const g of this.properties.values()) {
            if (g.has(SettingPropertyType.showPointLabel) && g.get(SettingPropertyType.showPointLabel).value) {
                showPointLabelEnabled = true;
                break;
            }
        }

        if (!showPointLabelEnabled) return true;

        // Then check if pointLabelType is 'free'
        for (const g of this.properties.values()) {
            if (
                g.has(SettingPropertyType.pointLabelType) &&
                g.get(SettingPropertyType.pointLabelType).value === 'free'
            ) {
                return false;
            }
        }

        return true;
    }

    /**
     * Checks if arc label type editing is disabled based on show arc label settings
     */
    get isArcLabelTypeDisabled() {
        for (const g of this.properties.values())
            if (g.has(SettingPropertyType.showArcLabel)) return !g.get(SettingPropertyType.showArcLabel).value;
        return true;
    }

    /**
     * Checks if angle arc editing is disabled based on angle type settings
     */
    get isAngleArcDisabled() {
        for (const g of this.properties.values())
            if (g.has(SettingPropertyType.showAngleTypes))
                return g.get(SettingPropertyType.showAngleTypes).value !== 'as2';
        return true;
    }

    /**
     * Checks if the selected angle is a right angle (90 degrees)
     */
    get isRightAngle() {
        if (!this.selectedElements.every(i => i.type === 'RenderAngle')) return false;

        for (const g of this.properties.values())
            if (g.has(SettingPropertyType.showAngleTypes))
                return g.get(SettingPropertyType.showAngleTypes).value === 'as1';
        return false;
    }

    /**
     * Checks if equal segment sign editing is disabled based on right angle status
     */
    get isEnableEqualSegmentSignDisabled() {
        return this.isRightAngle;
    }

    get isEqualSegmentSignDisabled() {
        if (this.isRightAngle) return true;

        for (const g of this.properties.values())
            if (g.has(SettingPropertyType.enableEqualSegmentSign)) {
                const enableEqualSegmentSign = g.get(SettingPropertyType.enableEqualSegmentSign).value;
                return typeof enableEqualSegmentSign === 'boolean' && enableEqualSegmentSign === false;
            }
        return false;
    }

    get updatePropTool(): UpdatePropTool | undefined {
        return this.tb.getTool('UpdatePropTool') as UpdatePropTool;
    }

    get renameElementTool(): RenameElementTool | undefined {
        return this.tb.getTool('RenameElementTool') as RenameElementTool;
    }

    get selectedElements() {
        return this.updatePropTool.toolState.selectedElements;
    }

    /**
     * Checks if right angle style options should be disabled based on angle measurements
     */
    get isRightAngleStyleOptionDisabled() {
        const elements = this.selectedElements;
        const angles: RenderAngle[] = [];
        for (const rel of elements) if (rel.type === 'RenderAngle') angles.push(rel as RenderAngle);

        return angles.length
            ? !angles.every(renderAngle => renderAngle.degree >= 89.5 && renderAngle.degree <= 90.5)
            : true;
    }

    get isFullViewport() {
        return this.tb.editor.geoEditorConf.docViewMode === 'full-viewport';
    }

    /**
     * Checks if multiple elements are selected
     */
    get isMixed() {
        return this.selectedElements.length > 1;
    }

    @HostListener('keydown', ['$event'])
    @HostListener('keyup', ['$event'])
    handleKeydown(event: KeyboardEvent): void {
        if (event.target['nodeName'] === 'INPUT') event.stopPropagation();
    }

    /**
     * Initializes the component by registering the tool listener and setting up the initial state
     */
    ngOnInit(): void {
        this.tb.registerToolListener(this.updatePropToolEventListener);

        if (!this.tb.isDisabled()) {
            this.applyNewDocRenderPropToState(this.updatePropTool.toolState);
            this.applyNewDocDefaultElRenderPropsToState(this.updatePropTool.toolState);
            this.replaceNewSelectedElementsToState(this.updatePropTool.toolState);
            this.cdr.markForCheck();
        }
    }

    ngOnDestroy(): void {
        this.tb.unregisterToolListener(this.updatePropToolEventListener);
    }

    /**
     * Gets the property value for a specific element and property type
     * @param e The geometric element
     * @param p The property type
     * @returns Object containing the property value and whether it's mixed
     */
    private getElementPropertyValue(e: GeoRenderElement, p: SettingPropertyType) {
        const value: { value: any; isMixed: boolean } = { value: e.renderProp[p], isMixed: false };
        return value;
    }

    /**
     * Handles changes to field values
     * @param data The field change data
     */
    onFieldChange(data: SettingFieldChangeEmitterData) {
        const { field, value } = data;

        switch (field) {
            case SettingPropertyType.pointLabelType:
            case SettingPropertyType.showPointLabel: {
                this.clearPointNameError();
                break;
            }
        }

        for (const [_, props] of this.properties.entries()) {
            if (field in props) {
                props[field].value = value;
            }
        }

        const updatePropToolState = this.updatePropTool.toolState;
        updatePropToolState.elementProps = { ...updatePropToolState.elementProps, [field]: value };
        updatePropToolState.notProcess = false;
        this.tb.update('UpdatePropTool', updatePropToolState);

        this.cdr.markForCheck();
    }

    /**
     * Handles changes to document-level field values
     * @param data The field change data
     */
    onDocFieldChange(data: SettingFieldChangeEmitterData) {
        switch (data.field) {
            case 'scale': {
                data.value /= 100;
                break;
            }
        }

        const { field, value } = data;

        const updatePropToolState = this.updatePropTool.toolState;
        updatePropToolState.docRenderProp = { ...updatePropToolState.docRenderProp, [field]: value };
        updatePropToolState.notProcess = false;
        this.tb.update('UpdatePropTool', updatePropToolState);
        this.cdr.markForCheck();
    }

    /**
     * Handles changes to document default element render properties
     * @param data The field change data
     */
    onDocDefaultElRenderPropsFieldChange(data: SettingFieldChangeEmitterData) {
        const { field, value } = data;
        const updatePropToolState = this.updatePropTool.toolState;
        updatePropToolState.docDefaultElRenderProps = {
            ...updatePropToolState.docDefaultElRenderProps,
            [field]: value,
        };
        updatePropToolState.notProcess = false;
        this.tb.update('UpdatePropTool', updatePropToolState);
        this.cdr.markForCheck();
    }

    /**
     * Resets document default element render properties
     */
    onDocDefaultElRenderPropsFieldReset() {
        const updatePropToolState = this.updatePropTool.toolState;
        updatePropToolState.docDefaultElRenderProps = undefined;
        updatePropToolState.notProcess = false;
        this.tb.update('UpdatePropTool', updatePropToolState);
        this.cdr.markForCheck();
    }

    /**
     * Handles input field changes
     * @param e The input event
     * @param field The property type
     */
    onInputChange(e: Event, field: SettingPropertyType) {
        const target = e.target as HTMLInputElement;
        const value = target.value;

        this.onFieldChange({ field, value });
    }

    /**
     * Handles point label input field changes
     * @param e The input event
     * @param field The property type
     */
    onPointLabelInputChange(e: Event, field: SettingPropertyType) {
        const target = e.target as HTMLInputElement;
        const value = target.value;

        // Validate point label
        const validationResult = this.validatePointLabel(value);
        if (!validationResult.valid) {
            // Show an error message
            this.pointLabelError = validationResult.message;
            return;
        } else {
            // Clear error message if valid
            this.pointLabelError = null;
        }

        this.onFieldChange({ field, value });
    }

    /**
     * Validates a point label
     * @param label The label to validate
     * @returns Validation result with a valid flag and optional error message
     */
    validatePointLabel(label: string): { valid: boolean; message?: string } {
        // Check for the maximum length (20 characters)
        if (label.length > 20) return { valid: false, message: 'Nội dung không được vượt quá 20 ký tự' };
        return { valid: true };
    }

    /**
     * Checks if element renaming is allowed for a specific property group
     * @param g The property group
     * @returns Boolean indicating if renaming is allowed
     */
    isAllowRenameElement(g: SettingPropertyGroup | 'Others'): boolean {
        if (g != SettingPropertyGroup.Point) return false;

        const pointGEls = this.selectedElements.filter(el => el.renderProp.group === SettingPropertyGroup.Point);
        if (pointGEls.length != 1 || g != SettingPropertyGroup.Point) return false;

        const toolState = this.renameElementTool.toolState;
        return toolState.docLocalId != null && isValidIdx(toolState.relIndex);
    }

    get elementName() {
        const toolState = this.renameElementTool.toolState;
        return toolState.name;
    }

    /**
     * Handles element renaming events
     * @param event The keyboard event
     */
    onRenameElement(_event: KeyboardEvent) {
        const value = this.elementNameInput.nativeElement.value.trim();
        const currentName = this.elementName;

        // Clear previous messages
        this.clearPointNameError();

        // Skip if the value hasn't changed from the current name, and it's not an Enter key press
        if (value === currentName) {
            this.cdr.markForCheck();
            return;
        }

        // Get the document controller
        const doc = this.updatePropTool.toolState.doc;
        if (!doc) {
            this.cdr.markForCheck();
            return;
        }

        // Use the validatePointName function to validate the point name
        const validationResult = validateName(
            doc, // document controller
            value, // name to validate
            [value], // input point names (just the current value)
            [], // names to avoid
            0, // index (default)
            'RenderVertex' // type of the element
        );

        // If the validation failed, show the error message
        if (!validationResult.valid) {
            this.pointNameError = validationResult.message || 'Tên điểm không hợp lệ.';
            this.cdr.markForCheck();
            return;
        }

        const toolState = this.renameElementTool.toolState;

        // Skip if the value hasn't changed from the current name, and it's not an immediate submission
        if (value === currentName) return;

        toolState.name = value;
        toolState.notProcess = false;
        this.tb.update('RenameElementTool', toolState);
        this.cdr.markForCheck();
    }

    /**
     * Clears the point name error message when the user focuses on the input field
     */
    clearPointNameError() {
        this.pointNameError = null;
    }

    /**
     * Updates the component state based on newly selected elements
     * @param state The current tool state
     */
    private replaceNewSelectedElementsToState(state: UpdatePropToolState) {
        this.properties.clear();

        // Properties list
        const pList = new Map<SettingPropertyType, SettingFieldValue>();

        // Elements group by setting group
        const gEls = new Map<SettingPropertyGroup, GeoRenderElement[]>();

        // Process each selected element
        for (const [idx, element] of state.selectedElements.entries()) {
            if (!gEls.has(element.renderProp.group)) gEls.set(element.renderProp.group, []);
            gEls.get(element.renderProp.group).push(element);

            // Get the set of available properties for the current element
            const elProps = new Set(
                Array.from(element.renderProp.settingProperties).filter(p =>
                    settingPropertyConfig[p].groups.has(element.renderProp.group)
                )
            );

            if (idx === 0) for (const p of elProps.values()) pList.set(p, this.getElementPropertyValue(element, p));
            else {
                for (const p of elProps) {
                    if (!pList.has(p)) {
                        pList.set(p, this.getElementPropertyValue(element, p));
                    } else {
                        const value = pList.get(p);
                        const value2 = this.getElementPropertyValue(element, p);
                        if (value.value !== value2.value) {
                            value.isMixed = true;
                            value.value = undefined;
                        }
                    }
                }
            }
        }

        // Set of groups of selected elements
        const gSet: Set<SettingPropertyGroup> = new Set(gEls.keys());

        for (const p of pList.keys()) {
            const pConfig = settingPropertyConfig[p];

            const pGroupsFiltered = Array.from(pConfig.groups).filter(g => gSet.has(g));
            const g = pGroupsFiltered.length === 1 ? pGroupsFiltered[0] : pConfig.multipleGroup ? 'Others' : undefined;
            const allowedMultipleGroup = g !== undefined;

            const isMultipleItems = pGroupsFiltered.reduce((acc, g) => acc + gEls.get(g).length, 0) !== 1;
            const allowedMutipleItem = !isMultipleItems || pConfig.multipleItem;

            const allowed = allowedMultipleGroup && allowedMutipleItem;

            if (!allowed) continue;

            if (!this.properties.has(g)) this.properties.set(g, new Map());
            this.properties.get(g).set(p, pList.get(p));
        }

        // Set the appropriate tab based on whether there are selected objects
        this.selectedTab = state.selectedElements.length ? 'obj' : 'geo';

        this.clearPointNameError();
    }

    // DOC PROPERTIES

    /** Map of document properties */
    readonly docProperties: {
        [key: string]: SettingFieldValue;
    } = {};

    /**
     * Checks if document properties should be shown
     */
    get isShowDocProperties() {
        return Object.keys(this.docProperties).length > 0 && this.selectedTab === 'geo';
    }

    /**
     * Applies new document render properties to the state
     * @param state The current tool state
     */
    private applyNewDocRenderPropToState(state: UpdatePropToolState) {
        const docRenderProp = state.docRenderProp;
        for (const key in docRenderProp ?? new UpdatePropToolState())
            switch (key) {
                case 'scale': {
                    this.docProperties[key] = {
                        value: Math.round(docRenderProp[key] * 100),
                        isMixed: false,
                    };
                    break;
                }
                default: {
                    this.docProperties[key] = {
                        value: docRenderProp[key],
                        isMixed: false,
                    };
                    break;
                }
            }
    }

    // DOC DEFAULT ELEMENT RENDER PROPS

    /** Map of document default element render properties */
    readonly docDefaultElRenderProps: Map<SettingPropertyType, SettingFieldValue> = new Map();

    /**
     * Applies a new document default element render properties to the state
     * @param state The current tool state
     */
    private applyNewDocDefaultElRenderPropsToState(state: UpdatePropToolState) {
        const docDefaultElRenderProps = state.docDefaultElRenderProps ?? new DefaultGeoRenderProp();
        for (const key of Object.getOwnPropertyNames(docDefaultElRenderProps))
            switch (key) {
                default: {
                    this.docDefaultElRenderProps.set(key as SettingPropertyType, {
                        value: docDefaultElRenderProps[key],
                        isMixed: false,
                    });
                    break;
                }
            }
    }
}
